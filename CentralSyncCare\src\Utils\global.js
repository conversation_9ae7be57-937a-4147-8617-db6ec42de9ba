let GlobalURL;

let LocalGlobalURL = [
  {
    //url: 'http://101.0.70.74/~synccare/central-syncare/public/api/v1/',
    //url: 'http://192.168.1.91:8000/api/v1/',
    url: 'https://staging.synccare.com.au/central-syncare/public/api/v1',  
    // videourl: 'http://staging.franchise.care/uploaded/videofile/',
    // paypalid: 'sb',
    // paypaltype: 'sandbox',
    version: '1.0',
  },
];

let LiveGlobalURL = [
  {
    url: 'https://synccare.io/central-syncare/public/api/v1/',
    // videourl: 'https://tfg.franchise.care/uploaded/videofile/',
    // paypalid: 'AZ1XjgwM4m-dg7L4OlQMf6jOdzq1H_6IDKLBVf7mh7VqIgJPsYSIKN03iC5LO2XtfSoLUac9NF5R0x8v',
    // paypaltype: 'live',
    version: '1.0',
  },
];

let StagingGlobalURL = [
  {
    url: 'https://staging.synccare.com.au/central-syncare/public/api/v1',
    //  url: 'http://psw.synccare.com.au/php/api',
    // videourl: 'http://staging.franchise.care/uploaded/videofile/',
    // paypalid: 'sb',
    // paypaltype: 'sandbox',
    version: '1.0',
  },
];

export default GlobalURL =
  window.location.hostname === 'localhost'
    ? LocalGlobalURL
    : window.location.hostname === 'staging.synccare.com.au'
    ? StagingGlobalURL
    : LiveGlobalURL;
