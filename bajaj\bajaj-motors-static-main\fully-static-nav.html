<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bajaj Motorcycles - Static Enhanced Mega Menu</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bajaj-blue': '#0047AB',
                        'bajaj-red': '#E31937',
                    }
                }
            }
        }
    </script>
    <style>
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 1.5rem;
            max-width: 100%;
        }

        @media (min-width: 768px) {
            .model-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 1.5rem;
            }
        }

        @media (min-width: 1024px) {
            .model-grid {
                grid-template-columns: repeat(5, 1fr);
                gap: 2rem;
            }
        }
        .category-btn.active {
            background-color: white;
            color: #0047AB;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tab-btn.active {
            color: #0047AB;
            border-bottom: 2px solid #0047AB;
        }
        .mobile-model-item:hover {
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .media-dropdown {
            min-width: 200px;
        }
        #mobile-overlay {
            z-index: 40;
        }
        #mobile-category-detail {
            z-index: 60;
        }
        .mobile-nav-section {
            transition: all 0.3s ease;
        }
        .category-chip {
            border: 1px solid #e5e7eb;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .category-chip.active {
            background-color: #0047AB;
            color: white;
            border-color: #0047AB;
        }
        .mega-menu-container {
            height: 500px;
            overflow: hidden;
        }
        .mega-menu-scrollable {
            height: 100%;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #0047AB #f1f1f1;
        }
        .mega-menu-scrollable::-webkit-scrollbar {
            width: 8px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-thumb {
            background: #0047AB;
            border-radius: 4px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
            background: #003380;
        }
        .categories-sidebar {
            height: 100%;
            overflow-y: auto;
        }
        .models-section {
            height: calc(100% - 50px);
        }
        .category-heading {
            grid-column: 1 / -1;
            text-align: left;
            padding: 10px 0 5px;
            font-weight: 600;
            color: #0047AB;
            margin-top: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        .category-heading:first-child {
            margin-top: 0;
        }
        .hero-section {
            background: linear-gradient(rgba(0, 71, 171, 0.9), rgba(0, 71, 171, 0.7)), url('https://images.unsplash.com/photo-1558981403-c5f9899a28bc?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
        }
        .model-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .model-item {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .model-item:hover {
            border-color: #0047AB;
            box-shadow: 0 4px 12px rgba(0, 71, 171, 0.1);
        }

        .model-item img {
            width: 100%;
            max-width: 100px;
            height: auto;
            object-fit: contain;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gray-800 text-white text-xs py-1">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <span>GOLDENIA GROUP WITH LEGACY OF 100 YEARS</span>
            <span>International website</span>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="bg-white shadow-md relative">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden p-2">
                    <i class="fas fa-bars text-xl text-gray-700"></i>
                </button>

                <!-- Logo -->
                <div class="flex items-center">
                    <div class="bg-bajaj-blue text-white px-3 py-1 rounded text-sm font-bold">
                        BAJAJ
                    </div>
                    <div class="ml-2 text-bajaj-blue text-sm font-semibold">
                        COME AS YOU ARE
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-8">
                    <!-- Motorcycles Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-bajaj-blue font-medium">
                            <span>MOTORCYCLES</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>

                        <!-- Mega Dropdown - Fixed Height Container -->
                        <div class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg border-t-2 border-bajaj-blue opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container">
                            <div class="flex h-full">
                                <!-- Categories Sidebar - Scrollable -->
                                <div class="w-48 bg-gray-100 p-4 categories-sidebar">
                                    <div class="space-y-2">
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded active" data-category="pulsar">
                                            PULSAR
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="dominar">
                                            DOMINAR
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="avengers">
                                            AVENGERS
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="discover">
                                            DISCOVER
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="platina">
                                            PLATINA
                                        </button>
                                    </div>
                                </div>

                                <!-- Models Section - Scrollable -->
                                <div class="mega-menu-scrollable flex-1 flex flex-col">
                                    <div class="p-6">
                                        <!-- Pulsar Models -->
                                        <div id="category-pulsar" class="category-content">
                                            <div class="flex space-x-6 mb-4 text-sm">
                                                <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="classic">Classic</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="ns">NS</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="n">N</button>
                                            </div>
                                            <div class="model-grid models-section">
                                                <!-- All Pulsar Models -->
                                                <div class="tab-content" data-tab="all">
                                                    <div class="category-heading">Classic</div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F" alt="PULSAR 220F ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 220F ABS</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD" alt="PULSAR 150 TD" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 150 TD</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150" alt="PULSAR 150" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 150</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125" alt="PULSAR 125" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 125</p>
                                                    </div>
                                                    
                                                    <div class="category-heading">NS</div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z" alt="PULSAR NS400Z" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS400Z</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI" alt="PULSAR NS 200 ABS FI" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 200 ABS FI</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" alt="PULSAR NS 200 ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 200 ABS</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160" alt="PULSAR NS 160 ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 160 ABS</p>
                                                    </div>
                                                    
                                                    <div class="category-heading">N</div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250" alt="PULSAR N250" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR N250</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160" alt="PULSAR N160" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR N160</p>
                                                    </div>
                                                </div>
                                                
                                                <!-- Classic Pulsar Models -->
                                                <div class="tab-content hidden" data-tab="classic">
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F" alt="PULSAR 220F ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 220F ABS</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD" alt="PULSAR 150 TD" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 150 TD</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150" alt="PULSAR 150" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 150</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125" alt="PULSAR 125" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR 125</p>
                                                    </div>
                                                </div>
                                                
                                                <!-- NS Pulsar Models -->
                                                <div class="tab-content hidden" data-tab="ns">
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z" alt="PULSAR NS400Z" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS400Z</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI" alt="PULSAR NS 200 ABS FI" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 200 ABS FI</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" alt="PULSAR NS 200 ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 200 ABS</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160" alt="PULSAR NS 160 ABS" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR NS 160 ABS</p>
                                                    </div>
                                                </div>
                                                
                                                <!-- N Pulsar Models -->
                                                <div class="tab-content hidden" data-tab="n">
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250" alt="PULSAR N250" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR N250</p>
                                                    </div>
                                                    <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                        <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160" alt="PULSAR N160" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                        <p class="text-xs font-medium text-gray-800 mb-1">PULSAR N160</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Dominar Models -->
                                        <div id="category-dominar" class="category-content hidden">
                                            <div class="flex space-x-6 mb-4 text-sm">
                                                <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="classic">Classic</button>
                                            </div>
                                            <div class="model-grid models-section">
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400" alt="DOMINAR 400" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">DOMINAR 400</p>
                                                </div>
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250" alt="DOMINAR 250" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">DOMINAR 250</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Avengers Models -->
                                        <div id="category-avengers" class="category-content hidden">
                                            <div class="flex space-x-6 mb-4 text-sm">
                                                <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="cruiser">Cruiser</button>
                                            </div>
                                            <div class="model-grid models-section">
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220" alt="AVENGER CRUISE 220" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">AVENGER CRUISE 220</p>
                                                </div>
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160" alt="AVENGER STREET 160" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">AVENGER STREET 160</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Discover Models -->
                                        <div id="category-discover" class="category-content hidden">
                                            <div class="flex space-x-6 mb-4 text-sm">
                                                <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="commuter">Commuter</button>
                                            </div>
                                            <div class="model-grid models-section">
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125" alt="DISCOVER 125" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">DISCOVER 125</p>
                                                </div>
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110" alt="DISCOVER 110" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">DISCOVER 110</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Platina Models -->
                                        <div id="category-platina" class="category-content hidden">
                                            <div class="flex space-x-6 mb-4 text-sm">
                                                <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                                                <button class="tab-btn text-gray-500 pb-1" data-tab="commuter">Commuter</button>
                                            </div>
                                            <div class="model-grid models-section">
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110" alt="PLATINA 110" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">PLATINA 110</p>
                                                </div>
                                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                                    <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100" alt="PLATINA 100" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                                    <p class="text-xs font-medium text-gray-800 mb-1">PLATINA 100</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">SHOWROOMS</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">WORKSHOPS</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">EVENTS</a>
                    <a href="#" class="bg-bajaj-blue text-white px-4 py-2 rounded font-medium hover:bg-blue-700">BOOK TEST RIDE</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">ABOUT US</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">NEWS</a>
                    
                    <!-- Media Center Dropdown - Desktop -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-bajaj-blue font-medium">
                            <span>MEDIA CENTER</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown">
                            <div class="py-2">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">ABOUT US</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">ANNOUNCEMENTS</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">EVENTS</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">BLOGS</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">DOWNLOAD CENTER</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">CONTACT US</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">FAQS</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile BIKES Button -->
                <div class="lg:hidden">
                    <span class="text-gray-700 font-medium">BIKES</span>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Overlay -->
        <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-nav-section fixed top-0 left-0 w-80 h-full bg-white z-50 transform -translate-x-full transition-transform duration-300">
            <div class="p-4">
                <button id="close-mobile-menu" class="absolute top-4 right-4 text-xl text-gray-600">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Mobile Menu Items -->
                <div class="mt-8 space-y-4">
                    <div class="mobile-dropdown">
                        <button class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2">
                            <span>BIKES</span>
                            <i class="fas fa-chevron-right text-xs"></i>
                        </button>
                        <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="pulsar">PULSAR</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="dominar">DOMINAR</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="avengers">AVENGERS</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="discover">DISCOVER</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="platina">PLATINA</button>
                        </div>
                    </div>

                    <a href="#" class="block py-2 font-medium text-gray-700">SHOWROOMS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">WORKSHOPS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">EVENTS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">BOOK TEST RIDE</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">ABOUT US</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">NEWS</a>

                    <!-- Media Center Dropdown - Mobile -->
                    <div class="mobile-dropdown">
                        <button class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2">
                            <span>MEDIA CENTER</span>
                            <i class="fas fa-chevron-right text-xs"></i>
                        </button>
                        <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                            <a href="#" class="block py-2 text-sm text-gray-600">ABOUT US</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">ANNOUNCEMENTS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">EVENTS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">DOWNLOAD CENTER</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">CONTACT US</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Category Detail View -->
        <div id="mobile-category-detail" class="mobile-nav-section fixed top-0 left-0 w-80 h-full bg-white z-60 transform -translate-x-full transition-transform duration-300">
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <button id="back-to-categories" class="mr-3 text-gray-600">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="close-category-detail" class="absolute top-4 right-4 text-xl text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                    <span id="category-title" class="font-medium text-gray-800">BIKES / PULSAR</span>
                </div>
                
                <!-- Category Switcher -->
                <div class="mb-6">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Switch Category:</h3>
                    <div class="flex flex-wrap gap-2" id="mobile-category-switcher">
                        <div class="category-chip active bg-bajaj-blue text-white" data-category="pulsar">PULSAR</div>
                        <div class="category-chip bg-gray-100 text-gray-700" data-category="dominar">DOMINAR</div>
                        <div class="category-chip bg-gray-100 text-gray-700" data-category="avengers">AVENGERS</div>
                        <div class="category-chip bg-gray-100 text-gray-700" data-category="discover">DISCOVER</div>
                        <div class="category-chip bg-gray-100 text-gray-700" data-category="platina">PLATINA</div>
                    </div>
                </div>

                <!-- Category Tabs -->
                <div id="mobile-category-tabs" class="flex space-x-4 mb-4 text-sm border-b pb-2">
                    <button class="tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1" data-tab="all">All</button>
                    <button class="tab-btn text-gray-500 pb-1" data-tab="classic">Classic</button>
                    <button class="tab-btn text-gray-500 pb-1" data-tab="ns">NS</button>
                    <button class="tab-btn text-gray-500 pb-1" data-tab="n">N</button>
                </div>

                <!-- Mobile Models List -->
                <div class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide">
                    <!-- All Models -->
                    <div class="tab-content" data-tab="all">
                        <div class="mt-4 mb-2 pl-3">
                            <div class="text-sm font-semibold text-bajaj-blue border-b border-gray-200 pb-1">
                                Classic
                            </div>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F" alt="PULSAR 220F ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 220F ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD" alt="PULSAR 150 TD" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 150 TD</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150" alt="PULSAR 150" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 150</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125" alt="PULSAR 125" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 125</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        
                        <div class="mt-4 mb-2 pl-3">
                            <div class="text-sm font-semibold text-bajaj-blue border-b border-gray-200 pb-1">
                                NS
                            </div>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z" alt="PULSAR NS400Z" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS400Z</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI" alt="PULSAR NS 200 ABS FI" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 200 ABS FI</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" alt="PULSAR NS 200 ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 200 ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160" alt="PULSAR NS 160 ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 160 ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        
                        <div class="mt-4 mb-2 pl-3">
                            <div class="text-sm font-semibold text-bajaj-blue border-b border-gray-200 pb-1">
                                N
                            </div>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250" alt="PULSAR N250" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR N250</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160" alt="PULSAR N160" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR N160</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Classic Models -->
                    <div class="tab-content hidden" data-tab="classic">
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F" alt="PULSAR 220F ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 220F ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD" alt="PULSAR 150 TD" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 150 TD</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150" alt="PULSAR 150" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 150</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125" alt="PULSAR 125" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR 125</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- NS Models -->
                    <div class="tab-content hidden" data-tab="ns">
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z" alt="PULSAR NS400Z" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS400Z</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI" alt="PULSAR NS 200 ABS FI" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 200 ABS FI</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" alt="PULSAR NS 200 ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 200 ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160" alt="PULSAR NS 160 ABS" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR NS 160 ABS</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- N Models -->
                    <div class="tab-content hidden" data-tab="n">
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250" alt="PULSAR N250" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR N250</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160" alt="PULSAR N160" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">PULSAR N160</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content - Hello World -->
    <main class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-8 text-center">
            <h1 class="text-4xl font-bold text-bajaj-blue mb-4">Hello World!</h1>
            <p class="text-gray-600 text-lg">Welcome to the Bajaj Motorcycles website.</p>
            <p class="text-gray-500 mt-4">This is a static implementation of the enhanced mega menu with all data embedded directly in HTML.</p>
            
            <div class="mt-8 flex justify-center">
                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
            </div>
        </div>
    </main>

    <script>
        // Mobile menu functionality
        const mobileOverlay = document.getElementById('mobile-overlay');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileCategoryDetail = document.getElementById('mobile-category-detail');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const closeMobileMenu = document.getElementById('close-mobile-menu');
        const backToCategories = document.getElementById('back-to-categories');
        const closeCategoryDetail = document.getElementById('close-category-detail');
        
        // Function to close mobile menu
        function closeMobileMenuFunc() {
            mobileMenu.classList.add('-translate-x-full');
            mobileCategoryDetail.classList.add('-translate-x-full');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
        
        // Open mobile menu
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.remove('-translate-x-full');
            mobileOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
        
        // Close mobile menu
        closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
        mobileOverlay.addEventListener('click', closeMobileMenuFunc);
        
        // Mobile dropdown functionality
        document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const content = btn.nextElementSibling;
                const icon = btn.querySelector('i');
                
                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            });
        });
        
        // Mobile category selection
        document.querySelectorAll('.mobile-category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;

                // Update category title
                document.getElementById('category-title').textContent = `BIKES / ${category.toUpperCase()}`;

                // Update category chips
                document.querySelectorAll('.category-chip').forEach(c => {
                    c.classList.remove('active', 'bg-bajaj-blue', 'text-white');
                    c.classList.add('bg-gray-100', 'text-gray-700');
                });
                const activeChip = document.querySelector(`.category-chip[data-category="${category}"]`);
                if (activeChip) {
                    activeChip.classList.remove('bg-gray-100', 'text-gray-700');
                    activeChip.classList.add('active', 'bg-bajaj-blue', 'text-white');
                }

                // Update tabs for the selected category
                updateMobileTabs(category);

                // Hide overlay when showing model list
                mobileOverlay.classList.add('hidden');

                // Transition to model view
                mobileMenu.classList.add('-translate-x-full');
                setTimeout(() => {
                    mobileCategoryDetail.classList.remove('-translate-x-full');
                }, 50);
            });
        });
        
        // Back to categories
        backToCategories.addEventListener('click', () => {
            mobileCategoryDetail.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileMenu.classList.remove('-translate-x-full');
                mobileOverlay.classList.remove('hidden');
            }, 50);
        });
        
        // Close category detail
        closeCategoryDetail.addEventListener('click', closeMobileMenuFunc);
        
        // Desktop category switching
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                
                // Remove active class from all buttons
                document.querySelectorAll('.category-btn').forEach(b => {
                    b.classList.remove('active', 'bg-white', 'text-bajaj-blue');
                    b.classList.add('text-gray-700');
                });
                
                // Add active class to clicked button
                btn.classList.remove('text-gray-700');
                btn.classList.add('active', 'bg-white', 'text-bajaj-blue');
                
                // Hide all category contents
                document.querySelectorAll('.category-content').forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show the selected category content
                document.getElementById(`category-${category}`).classList.remove('hidden');
            });
        });
        
        // Tab switching functionality (desktop and mobile)
        function setupTabSwitching(container) {
            const tabs = container.querySelectorAll('.tab-btn');
            const tabContents = container.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabName = tab.dataset.tab;
                    
                    // Update active tab styling
                    tabs.forEach(t => {
                        t.classList.remove('text-bajaj-blue', 'border-b-2', 'border-bajaj-blue', 'active');
                        t.classList.add('text-gray-500');
                    });
                    tab.classList.remove('text-gray-500');
                    tab.classList.add('text-bajaj-blue', 'border-b-2', 'border-bajaj-blue', 'active');
                    
                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // Show the selected tab content
                    const selectedTabContent = container.querySelector(`.tab-content[data-tab="${tabName}"]`);
                    if (selectedTabContent) {
                        selectedTabContent.classList.remove('hidden');
                    }
                });
            });
        }
        
        // Initialize tab switching for desktop mega menu
        document.querySelectorAll('.category-content').forEach(content => {
            setupTabSwitching(content);
        });
        
        // Initialize tab switching for mobile category detail
        setupTabSwitching(document.getElementById('mobile-category-detail'));

        // Initialize mobile content with default category (pulsar)
        updateMobileTabs('pulsar');
        
        // Function to update mobile tabs and content based on category
        function updateMobileTabs(category) {
            const tabsContainer = document.getElementById('mobile-category-tabs');
            const mobileContentContainer = document.querySelector('#mobile-category-detail .space-y-3');
            const desktopCategoryContent = document.getElementById(`category-${category}`);

            if (!desktopCategoryContent) return;

            // Get tabs from the desktop version of this category
            const desktopTabs = desktopCategoryContent.querySelectorAll('.tab-btn');

            // Clear existing mobile tabs
            tabsContainer.innerHTML = '';

            // Create new mobile tabs based on desktop tabs
            desktopTabs.forEach((desktopTab, index) => {
                const button = document.createElement('button');
                button.className = index === 0
                    ? 'tab-btn text-bajaj-blue border-b-2 border-bajaj-blue active pb-1'
                    : 'tab-btn text-gray-500 pb-1';
                button.setAttribute('data-tab', desktopTab.getAttribute('data-tab'));
                button.textContent = desktopTab.textContent;
                tabsContainer.appendChild(button);
            });

            // Update mobile content based on category
            updateMobileContent(category, mobileContentContainer);

            // Re-initialize tab switching for the new tabs
            setupTabSwitching(document.getElementById('mobile-category-detail'));
        }

        // Function to update mobile content based on category
        function updateMobileContent(category, container) {
            const desktopCategoryContent = document.getElementById(`category-${category}`);
            if (!desktopCategoryContent) return;

            // Clear existing content
            container.innerHTML = '';

            // Get all tab contents from desktop version
            const desktopTabContents = desktopCategoryContent.querySelectorAll('.tab-content');

            desktopTabContents.forEach((tabContent, index) => {
                const mobileTabContent = document.createElement('div');
                mobileTabContent.className = index === 0 ? 'tab-content' : 'tab-content hidden';
                mobileTabContent.setAttribute('data-tab', tabContent.getAttribute('data-tab'));

                // Convert desktop grid items to mobile list items
                const desktopItems = tabContent.querySelectorAll('.model-item');
                const categoryHeadings = tabContent.querySelectorAll('.category-heading');

                // Handle "All" tab with category headings
                if (tabContent.getAttribute('data-tab') === 'all') {
                    let currentHeading = null;

                    Array.from(tabContent.children).forEach(child => {
                        if (child.classList.contains('category-heading')) {
                            // Add heading
                            const headingDiv = document.createElement('div');
                            headingDiv.className = 'mt-4 mb-2 pl-3';
                            headingDiv.innerHTML = `<div class="text-sm font-semibold text-bajaj-blue border-b border-gray-200 pb-1">${child.textContent}</div>`;
                            mobileTabContent.appendChild(headingDiv);
                        } else if (child.classList.contains('model-item')) {
                            // Convert to mobile item
                            const mobileItem = createMobileItem(child);
                            mobileTabContent.appendChild(mobileItem);
                        }
                    });
                } else {
                    // Handle other tabs without headings
                    desktopItems.forEach(item => {
                        const mobileItem = createMobileItem(item);
                        mobileTabContent.appendChild(mobileItem);
                    });
                }

                container.appendChild(mobileTabContent);
            });
        }

        // Function to create mobile item from desktop item
        function createMobileItem(desktopItem) {
            const img = desktopItem.querySelector('img');
            const text = desktopItem.querySelector('p');

            const mobileItem = document.createElement('div');
            mobileItem.className = 'mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer';

            mobileItem.innerHTML = `
                <img src="${img.src}" alt="${img.alt}" class="w-16 h-10 object-cover rounded">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-800">${text.textContent}</div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            `;

            return mobileItem;
        }

        // Mobile category switcher
        document.querySelectorAll('.category-chip').forEach(chip => {
            chip.addEventListener('click', () => {
                const category = chip.dataset.category;

                // Update active chip
                document.querySelectorAll('.category-chip').forEach(c => {
                    c.classList.remove('active', 'bg-bajaj-blue', 'text-white');
                    c.classList.add('bg-gray-100', 'text-gray-700');
                });
                chip.classList.remove('bg-gray-100', 'text-gray-700');
                chip.classList.add('active', 'bg-bajaj-blue', 'text-white');

                // Update category title
                document.getElementById('category-title').textContent = `BIKES / ${category.toUpperCase()}`;

                // Update tabs for the selected category
                updateMobileTabs(category);
            });
        });
        
        // Model item click handlers
        document.addEventListener('click', (e) => {
            if (e.target.closest('.model-item') || e.target.closest('.mobile-model-item')) {
                const modelElement = e.target.closest('.model-item, .mobile-model-item');
                const modelName = modelElement.querySelector('p, .text-sm').textContent;
                alert(`You selected: ${modelName}\n\nThis would typically navigate to the model details page.`);
            }
        });
    </script>
</body>
</html>