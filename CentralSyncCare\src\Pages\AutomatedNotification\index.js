import { Box, Button, Card, Collapse, FormControl, Grid, InputLabel, MenuItem, Select, Snackbar, styled, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react'
import TableComponent from '../../Components/TableComponent';
import { Add, <PERSON><PERSON><PERSON><PERSON>, Close, FilterList } from '@mui/icons-material';
import httpclient from '../../Utils';
import MuiAlert from "@mui/material/Alert";
import EditAutomatedNotification from '../../Components/EditAutomatedNotification';
import DeleteDialog from '../../Components/DeleteDialog';



const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const AutomatedPushNotification = () => {
    const [rows, setRows] = useState([]);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("success");
    const [rowLoading, setRowLoading] = useState({});
    const [loading, setLoading] = useState(false);
    const [isAddNew, setIsAddNew] = useState(false);


    const columns = [
        { id: "id", name: "SN" },
        { id: "title", name: "Title" },
        { id: "notify_type", name: "Notification Type" },
        { id: "message", name: "Message" },
        { id: "status", name: "Status" },
        { id: "last_pushed_date", name: "Last Pushed Date" },
        // { id: "view", name: "View" },
        { id: "actions", name: "Actions" },
    ];


    useEffect(() => {
        getNotificationList();
    }, []);


    const getNotificationList = async () => {
        try {
            const response = await httpclient("request-response?requestName=lightspeed/automated/notifications");
            const apiData = response.data?.data || [];

            const formattedData = apiData.map((item) => ({
                id: item.id,
                title: item.title,
                notify_type: item.notify_type,
                notify_type_id: item.notify_type_id,
                message: item.message,
                status: item.status,
                last_pushed: item.last_pushed_date,
                actions: "Edit | Delete"
            }));

            setRows(formattedData);
        } catch (error) {
            console.error("Failed to fetch notifications", error);
        }
    };


    const handleAddNew = () => {
        setIsAddNew(true);
        setOpenEditDialog(true);
        setViewDetails({});
    };

    const handleEdit = (row) => {
        setOpenEditDialog(true);
        console.log(row)
        setViewDetails(row);
    };


    const sendEdit = (call, formData) => {
        if (call.open === false) {
            setOpenEditDialog(false);
            setViewDetails({});
            setIsAddNew(false);
        }
        if (call.success === true) {
            if (isAddNew) {
                // Create new notification
                httpclient
                    .post(`request-response?requestName=lightspeed/automated/notifications`, formData)
                    .then((response) => {
                        if (response.status === 200) {
                            getNotificationList();
                            setOpen(true);
                            setMessageState("success");
                            setMessage(response.data?.message || "Added successfully");
                            setViewDetails({});
                        } else {
                            setOpen(true);
                            setMessage(response.data?.message || "An error occurred");
                            setMessageState("error");
                            setLoading(false);
                        }
                    }).catch((err) => {
                        setOpen(true);
                        setMessage(err?.response?.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                    });
            } else if (viewDetails.id) {
                // Update existing notification
                httpclient
                    .put(`request-response?requestName=lightspeed/automated/notification/update/${viewDetails.id}`, formData)
                    .then((response) => {
                        if (response.status === 200) {
                            getNotificationList();
                            setOpen(true);
                            setMessageState("success");
                            setMessage(response.data?.message || "Added successfully");
                            setViewDetails({});
                        } else {
                            setOpen(true);
                            setMessage(response.data?.message || "An error occurred");
                            setMessageState("error");
                            setLoading(false);
                        }
                    }).catch((err) => {
                        setOpen(true);
                        setMessage(err?.response?.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                    });
            }
        }
    };

    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row);
    };

    const sendDelete = (call) => {
        if (call.open === false) {
            setOpenDeleteDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            httpclient
                .delete(`request-response?requestName=lightspeed/delete/automated-notification/${viewDetails.id}`)
                .then((response) => {
                    if (response.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(response?.data?.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getNotificationList();
                    } else {
                        setOpen(true);
                        setMessage(response?.data?.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                }).catch((err) => {
                    setOpen(true);
                    setMessage(err?.response?.data?.message || "An error occurred");
                    setMessageState("error");
                    setLoading(false);
                });
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const updateRowStatus = async (row) => {
        console.log(row)
        try {
            setRowLoading((prev) => ({ ...prev, [row.id]: true }));

            // Toggle the status (assuming status is 0 or 1)
            const newStatus = row.status === 1 ? 0 : 1;

            const payload = {
                status: newStatus,
                notify_type_id: row.notify_type_id|| '',
                title: row.title || '',
                message: row.message || '',
            };


            // Make API call to update status
            const response = await httpclient.put(
                `request-response?requestName=lightspeed/automated/notification/update/${row.id}`,
                payload
            );

            if (response.status === 200) {
                // Show success message
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Status updated successfully");

                // Refresh the notification list
                await getNotificationList();
            } else {
                // Show error message
                setOpen(true);
                setMessage(response.data?.message || "Failed to update status");
                setMessageState("error");
            }
        } catch (error) {
            // Handle error
            setOpen(true);
            setMessage(error?.response?.data?.message || "An error occurred");
            setMessageState("error");
        } finally {
            setRowLoading((prev) => ({ ...prev, [row.id]: false }));
        }
    };

    const currentChange = (value, row) => {
        if (value === "allow_update") {
            handleEdit(row);
        }
        if (value === "allow_delete") {
            handleDelete(row);
        }
    };


    // Dummy functions required by TableComponent
    const dummyFunction = () => { };


    return (
        <Box>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>List Automated Push Notification</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <AddButton
                        color="primary"
                        variant="contained"
                        onClick={handleAddNew}
                    >
                        <Add style={{ marginRight: "5px" }} fontSize="small" /> Add Notification
                    </AddButton>
                </Grid>

                {/* Filter */}
                {/* <Grid item xs={12}>
                                <Collapse in={filterOpen}>
                                    <Card>
                                        <Box p={4}>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} md={4}>
                                                    <InputLabel>Title</InputLabel>
                                                    <TextField
                                                        variant="outlined"
                                                        name="notification_title"
                                                        value={filterData.notification_title}
                                                        onChange={handleChangeFilter}
                                                        onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                                        fullWidth
                                                    />
                                                </Grid>
                                                <Grid item xs={12} md={4}>
                                                    <InputLabel>Push To LiteCard?</InputLabel>
                                                    <FormControl fullWidth>
                                                        <Select
                                                            name="is_created_schedule"
                                                            value={filterData.is_created_schedule}
                                                            onChange={handleChangeFilter}
                                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                                        >
                                                            <MenuItem value={""}>Select</MenuItem>
                                                            <MenuItem value={"1"}>Yes</MenuItem>
                                                            <MenuItem value={"0"}>No</MenuItem>
            
                                                        </Select>
                                                    </FormControl>
            
            
            
                                                </Grid>
                                                <Grid item xs={12} md={4}>
                                                    <InputLabel>Start Date</InputLabel>
                                                    <TextField
                                                        variant="outlined"
                                                        name="startDate"
                                                        type="date"
                                                        value={filterData.startDate}
                                                        onChange={(e) => handleChangeFilter(e)}
                                                        fullWidth
                                                        InputLabelProps={{
                                                            shrink: true,
                                                        }}
                                                    />
                                                </Grid>
            
                                                <Grid item xs={12} md={4}>
                                                    <InputLabel>End Date</InputLabel>
                                                    <TextField
                                                        variant="outlined"
                                                        name="endDate"
                                                        type="date"
                                                        value={filterData.endDate}
                                                        onChange={(e) => handleChangeFilter(e)}
                                                        fullWidth
                                                        InputLabelProps={{
                                                            shrink: true,
                                                        }}
                                                    />
                                                </Grid>
            
            
            
                                                <Grid item xs={12}>
                                                    <Box textAlign={"right"}>
                                                        <Button
                                                            variant="contained"
                                                            color="primary"
                                                            onClick={handleFilter}
                                                        >
                                                            Filter{" "}
                                                            <ArrowForward
                                                                fontSize="small"
                                                                style={{ marginLeft: "5px" }}
                                                            />
                                                        </Button>
                                                    </Box>
                                                </Grid>
                                            </Grid>
                                        </Box>
                                    </Card>
                                </Collapse>
                            </Grid> */}

                {/* {submittedData.notification_id ||
                                submittedData.notification_title ||
                                submittedData.is_created_schedule ||
                                submittedData.startDate ? (
                                <Grid item xs={12}>
                                    <FilteredBox>
                                        <span>Filtered: </span>
                                        {submittedData.notification_id && (
                                            <p>
                                                <span>Rule ID: {submittedData.notification_id}</span>
                                                <Close
                                                    fontSize="small"
                                                    onClick={() => handleRemove("notification_id")}
                                                />
                                            </p>
                                        )}
                                        {submittedData.notification_title && (
                                            <p>
                                                <span>Title: {submittedData.notification_title}</span>
                                                <Close
                                                    fontSize="small"
                                                    onClick={() => handleRemove("notification_title")}
                                                />
                                            </p>
                                        )}
                                        {submittedData.is_created_schedule && (
                                            <p>
                                                <span>Push To LiteCard?: {submittedData.is_created_schedule === "1" ? "Yes" : "No"}</span>
                                                <Close
                                                    fontSize="small"
                                                    onClick={() => handleRemove("is_created_schedule")}
                                                />
                                            </p>
                                        )}
                                        {(submittedData.startDate || submittedData.endDate) && (
                                            <p>
                                                <span>
                                                    Scheduled Date Range: {submittedData.startDate} -{" "}
                                                    {submittedData.endDate}
                                                </span>
                                                <Close
                                                    fontSize="small"
                                                    onClick={() => handleRemove("startDate")}
                                                />
                                            </p>
                                        )}
            
                                    </FilteredBox>
                                </Grid>
                            ) : (
                                <Box></Box>
                            )} */}
                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        name={"Notification"}
                        columns={columns}
                        rows={rows}
                        sort={false}
                        loading={loading}
                        rowLoading={rowLoading}
                        setRowLoading={setRowLoading}
                        updateRowStatus={updateRowStatus}
                        props={{
                            permissions: [
                                { name: "allow_update", status: 1 },
                                { name: "allow_delete", status: 1 }
                            ]
                        }}
                        options={[
                            { name: "allow_update", status: 1 },
                            { name: "allow_delete", status: 1 }
                        ]}
                        currentChange={currentChange}
                        footer={false}
                        page={0}
                        rowsPerPage={10}
                        total={rows.length}
                        fromTable={1}
                        toTable={rows.length}
                        handleChangePage={dummyFunction}
                        handleChangeRowsPerPage={dummyFunction}
                    />
                </Grid>
                {/* <Footer overlay={overlay || props.overlayNew} /> */}
            </Grid>



            {openDeleteDialog && <DeleteDialog name={"Notification"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditAutomatedNotification
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>

        </Box>
    )
}

export default AutomatedPushNotification