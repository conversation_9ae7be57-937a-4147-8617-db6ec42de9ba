import React, { useState, useEffect } from 'react';
import {
    <PERSON>rid, <PERSON><PERSON><PERSON>, <PERSON>, Box, InputLabel, TextField, Button, Chip, Typography, Paper,
    Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Snackbar, CircularProgress,
    TablePagination
} from '@mui/material';
import { ArrowForward, FilterList } from '@mui/icons-material';
import MuiAlert from "@mui/material/Alert";
import httpclient from '../../../Utils';
import moment from 'moment';

export default function AutomatedPushNotificationHistory() {
    const [filterOpen, setFilterOpen] = useState(false);
    const [filterData, setFilterData] = useState({
        startDate: '',
        endDate: ''
    });
    const [submittedData, setSubmittedData] = useState({});
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState('');
    const [messageState, setMessageState] = useState('success');
    const [pagination, setPagination] = useState({
        page: 0,
        rowsPerPage: 20,
        total: 0
    });

    const fetchData = async (filters = {}, page = 0, rowsPerPage = 20) => {
        try {
            setLoading(true);
            let url = 'request-response?requestName=lightspeed/notification/history';
            url += `&page=${page + 1}&per_page=${rowsPerPage}`;

            if (filters.startDate) url += `&start_date=${filters.startDate}`;
            if (filters.endDate) url += `&end_date=${filters.endDate}`;

            const res = await httpclient.get(url);

            if (res?.data?.status === 200) {
                const apiData = res.data.data.data;
                const formatted = apiData.map((item, index) => ({
                    sn: (page * rowsPerPage) + index + 1,
                    title: item?.notification?.title || '-',
                    store_id: item?.store_id || '-',
                    email: item?.email || '-',
                    message_sent: item?.message_sent || 0,
                    last_sent: moment(item?.created_at).format("YYYY-MM-DD hh:mm A")
                }));
                setData(formatted);
                setPagination(prev => ({
                    ...prev,
                    total: res.data.data.total,
                    page,
                    rowsPerPage
                }));
                // setMessage('Data loaded successfully');
                // setMessageState('success');
            } else {
                setData([]);
                setMessage('Failed to load data');
                setMessageState('error');
            }
        } catch (err) {
            console.error(err);
            setMessage('Error fetching data');
            setMessageState('error');
        } finally {
            // setOpen(true);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData(submittedData, pagination.page, pagination.rowsPerPage);
    }, [pagination.page, pagination.rowsPerPage, submittedData]);

    const handleChangeFilter = (e) => {
        setFilterData({ ...filterData, [e.target.name]: e.target.value });
    };

    const handleFilter = () => {
        setSubmittedData(filterData);
        setPagination(prev => ({ ...prev, page: 0 }));
        fetchData(filterData, 0, pagination.rowsPerPage);
    };

    const handleRemove = (key) => {
        const updated = { ...submittedData, [key]: '' };
        setSubmittedData(updated);
        const newFilters = { ...filterData, [key]: '' };
        setFilterData(newFilters);
        setPagination(prev => ({ ...prev, page: 0 }));
        fetchData(updated, 0, pagination.rowsPerPage);
    };

    const handleClose = (_, reason) => {
        if (reason === 'clickaway') return;
        setOpen(false);
    };

    const handleChangePage = (_, newPage) => {
        setPagination(prev => ({ ...prev, page: newPage }));
    };

    const handleChangeRowsPerPage = (event) => {
        const newRowsPerPage = parseInt(event.target.value, 10);
        setPagination({
            ...pagination,
            rowsPerPage: newRowsPerPage,
            page: 0
        });
    };

    return (
        <Box>
            <Grid container alignItems="center" justifyContent="space-between" mb={2}>
                <Grid item>
                    <Typography variant="h4" fontWeight="bold">
                        Automated Push Notification History
                    </Typography>
                </Grid>
                <Grid item>
                    <Button color="primary" variant="contained" onClick={() => setFilterOpen(!filterOpen)}>
                        Filter <FilterList sx={{ ml: 1 }} fontSize="small" />
                    </Button>
                </Grid>
            </Grid>

            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={3}>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Start Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="startDate"
                                            type="date"
                                            value={filterData.startDate}
                                            onChange={handleChangeFilter}
                                            fullWidth
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={4}>
                                        <InputLabel>End Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="endDate"
                                            type="date"
                                            value={filterData.endDate}
                                            onChange={handleChangeFilter}
                                            fullWidth
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    </Grid>

                                    <Grid item xs={12}>
                                        <Box textAlign="right">
                                            <Button variant="contained" onClick={handleFilter}>
                                                Filter <ArrowForward sx={{ ml: 1 }} />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {(submittedData.startDate || submittedData.endDate) && (
                    <Grid item xs={12}>
                        <Box display="flex" flexWrap="wrap" gap={1} mt={2}>
                            {submittedData.startDate && (
                                <Chip label={`Start Date: ${submittedData.startDate}`} onDelete={() => handleRemove('startDate')} />
                            )}
                            {submittedData.endDate && (
                                <Chip label={`End Date: ${submittedData.endDate}`} onDelete={() => handleRemove('endDate')} />
                            )}
                        </Box>
                    </Grid>
                )}
            </Grid>

            {loading ? (
                <Box textAlign="center" py={4}>
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    <TableContainer component={Paper} sx={{ mt: 3 }}>
                        <Table>
                            <TableHead sx={{ backgroundColor: '#f3f4f6' }}>
                                <TableRow>
                                    <TableCell><strong>S.N</strong></TableCell>
                                    <TableCell><strong>Title</strong></TableCell>
                                    <TableCell><strong>Store</strong></TableCell>
                                    <TableCell><strong>Email</strong></TableCell>
                                    <TableCell><strong>Message Sent</strong></TableCell>
                                    <TableCell><strong>Last Sent Date</strong></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.map(item => (
                                    <TableRow key={item.sn}>
                                        <TableCell>{item.sn}</TableCell>
                                        <TableCell>{item.title}</TableCell>
                                        <TableCell>{item.store_id}</TableCell>
                                        <TableCell>{item.email}</TableCell>
                                        <TableCell>{item.message_sent}</TableCell>
                                        <TableCell>{item.last_sent}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={pagination.total}
                        page={pagination.page}
                        onPageChange={handleChangePage}
                        rowsPerPage={pagination.rowsPerPage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        rowsPerPageOptions={[20, 50, 70, 100]}
                    />
                </>
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleClose}
            >
                <MuiAlert onClose={handleClose} severity={messageState} sx={{ width: '100%' }}>
                    {message}
                </MuiAlert>
            </Snackbar>
        </Box>
    );
}
