{"name": "openapi-types", "version": "12.1.3", "description": "Types for OpenAPI documents.", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"prepare": "../../bin/tsc", "test-watch": "../../bin/tsc"}, "keywords": ["openapi", "swagger", "types", "typescript"], "author": "<PERSON>", "bugs": {"url": "https://github.com/kogosoftwarellc/open-api/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+label%3Aopenapi-types"}, "repository": "https://github.com/kogosoftwarellc/open-api/tree/master/packages/openapi-types", "homepage": "https://github.com/kogosoftwarellc/open-api/tree/master/packages/openapi-types#readme", "license": "MIT", "gitHead": "09fed6d77536b3d7d1d38a2630a096041da773ee"}