# openapi-types Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## 1.4.0 - 2021-01-05
### Added
- Added an index signature to `OperationObject` to allow access to extensions.

### Fixed
- Added `undefined` to the index signature for `PathsObject` to prevent unsafe null access when `strictNullChecks` is enabled.

## 1.3.5 - 2019-05-13
### Fixed
- Amended missing usage of PathsObject in OpenAPIV3.Document interface.

## 1.3.4 - 2019-01-31
### Fixed
- OpenAPIV3: relax security requirement object types (#327)

## 1.3.3 - 2019-01-22
### Fixed
- Allowing to set a property of BaseSchemaObject as a reference to another SchemaObject (#312)

## 1.3.2 - 2018-10-17
### Added
- Added `Operation` to `OpenAPI` namespace.

## 1.3.1 - 2018-10-03
### Fixed
- Updating .npmignore to publish `dist`

## 1.3.0 - 2018-10-03
### Added
- `OpenAPI.Parameter` - Represents a parameter across all OpenAPI versions that have the notion of a parameter.

## 1.2.0 - 2018-09-29
### Added
- `OpenAPI.Parameters` - Represents parameters across all OpenAPI versions that have the notion of parameters.
- exporting `OpenAPIV2.Parameters` and `OpenAPIV2.Parameter`.
