<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bike Carousel</title>
  <style>
    /* Top Bar Responsive Styles */
    .top-bar {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    @media (min-width: 1024px) {
      .top-bar {
        padding-left: 150px;
        padding-right: 150px;
      }
    }

    .top-bar-transparent {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .top-bar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .top-bar-left,
    .top-bar-right {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .top-bar-text {
      font-size: 0.875rem;
      font-weight: bold;
      color: #fafafa;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .top-bar-logo {
      height: 2rem;
      width: auto;
      flex-shrink: 0;
    }

    .top-bar-icon {
      width: 23px;
      height: 18px;
      flex-shrink: 0;
    }

    .floating-navbar {
      margin: 0 auto;
      height: 110px;
      padding-left: 0;
      padding-right: 0;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    @media (min-width: 1024px) {
      .floating-navbar {
        margin-left: 150px;
        margin-right: 150px;
      }
    }

    .nav-bg {
      background: linear-gradient(to bottom, #ffffff 0%, #ebebeb 100%);
    }

    .model-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 1rem;
    }

    .category-btn.active {
      color: #222222;
      text-decoration: underline;
      text-underline-offset: 4px;
    }

    .tab-btn,
    .variant-btn {
      padding: 0.5rem 1rem;
      font-size: 1rem;
      color: #4b5563;
      white-space: nowrap;
    }

    .tab-btn.active,
    .variant-btn.active {
      color: #222222;
      border-bottom: 2px solid #222222;
    }

    .mobile-model-item:hover {
      background-color: #f9fafb;
    }

    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }

    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .media-dropdown {
      min-width: 200px;
    }

    #mobile-overlay {
      z-index: 40;
    }

    #mobile-category-detail {
      z-index: 60;
    }

    .mobile-nav-section {
      transition: all 0.3s ease;
    }

    .mega-menu-container {
      height: 500px;
      overflow: hidden;
    }

    .mega-menu-scrollable {
      height: 100%;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: #0047ab #f1f1f1;
    }

    .mega-menu-scrollable::-webkit-scrollbar {
      width: 8px;
    }

    .mega-menu-scrollable::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    .mega-menu-scrollable::-webkit-scrollbar-thumb {
      background: #0047ab;
      border-radius: 4px;
    }

    .mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
      background: #003380;
    }

    .categories-sidebar {
      height: 100%;
      overflow-y: auto;
    }

    .models-section {
      height: calc(100% - 50px);
    }

    .category-heading {
      grid-column: 1 / -1;
      text-align: left;
      padding: 10px 0 5px;
      font-weight: 600;
      margin-top: 15px;
      width: fit-content;
    }

    .category-heading:first-child {
      margin-top: 0;
    }

    #brand-tabs-container::-webkit-scrollbar,
    #model-tabs-container::-webkit-scrollbar {
      display: none;
    }

    #brand-tabs-container,
    #model-tabs-container {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .scroll-indicator {
      position: absolute;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .scroll-indicator:hover {
      transform: translateX(-50%) scale(1.1);
    }

    .bounce {
      animation: bounce 2s infinite;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }

    .scroll-indicator-icon {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .scroll-indicator-icon svg {
      width: 24px;
      height: 24px;
    }

    .pulsar-text {
      font-size: 20vw;
    }

    @media (min-width: 1024px) {
      .pulsar-text {
        font-size: 25vw;
      }
    }

    .other-brand-text {
      font-size: 15vw;
    }

    @media (min-width: 1024px) {
      .other-brand-text {
        font-size: 18vw;
      }
    }

    .featured-text-column {
      grid-column: 1 / span 3;
    }

    .featured-image-column {
      grid-column: 4 / span 7;
    }

    .featured-text-column,
    .featured-image-column {
      transition: transform 0.3s ease;
    }

    .featured-text-column:hover,
    .featured-image-column:hover {
      transform: translateY(-5px);
    }

    .expreience-carousel-slide {
      transition: all 0.6s ease;
    }

    .expreience-slide-image {
      transition: transform 0.3s ease;
    }

    .expreience-carousel-slide:hover .expreience-slide-image {
      transform: scale(1.05);
    }

    .expreience-explore-btn {
      transition: all 0.3s ease;
    }

    .explore-btn:hover {
      transform: translateY(-2px);
    }

    .expreience-nav-button {
      transition: all 0.3s ease;
    }

    .expreience-nav-button:hover {
      transform: translateY(-50%) scale(1.1);
    }

    .expreience-nav-button:active {
      transform: translateY(-50%) scale(0.95);
    }

    @media (max-width: 1024px) {
      .featured-text-column,
      .featured-image-column {
        grid-column: 1 / -1;
      }
    }
  </style>
</head>
<body>
  <section id="bike-carousel" class="w-full bg-white relative overflow-hidden">
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <h1 class="brand-text pulsar-text text-[15vw] lg:text-[20vw] font-black text-indigo-100 select-none" id="background-brand-text">
        PULSAR
      </h1>
    </div>

    <div class="relative z-10 pt-8 px-12">
      <nav class="flex justify-center">
        <div class="inline-flex space-x-8 lg:space-x-16 border-b border-gray-200 pl-8 pr-8 overflow-x-auto whitespace-nowrap lg:overflow-x-visible lg:whitespace-normal scrollbar-hide" id="brand-tabs-container"></div>
      </nav>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 py-8">
      <div class="text-center mb-8">
        <h2 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4" id="bike-title">
          PULSAR 220F ABS
        </h2>
        <p class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed" id="bike-description">
          Experience the perfect blend of power and style with the Pulsar 220F ABS.
        </p>
      </div>

      <div class="grid grid-cols-12 gap-4 lg:gap-8 items-center min-h-[500px]">
        <div class="col-span-2 flex justify-center items-center">
          <button class="bike-prev-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200" id="bike-prev-btn">
            <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
        </div>

        <div class="col-span-8 flex justify-center items-center">
          <div class="relative w-full max-w-4xl">
            <img src="./assets/bikes/pulsar/pulsar_220f_abs.png" alt="Pulsar 220F ABS" class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain" id="main-bike-image" />
          </div>
        </div>

        <div class="col-span-2 flex justify-center items-center">
          <button class="bike-next-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200" id="bike-next-btn">
            <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>

      <div class="mt-8">
        <div class="flex items-center justify-between mb-6 px-4 gap-4 flex-wrap md:flex-nowrap">
          <div class="flex items-center space-x-4 min-w-0">
            <img src="./assets/brand-logos/pulsar-logo.png" alt="Pulsar Logo" class="h-8" id="brand-logo" />
            <div class="flex items-center space-x-2">
              <img src="./assets/icons/sports.png" alt="Sports Category" class="h-6" id="category-icon" />
              <span class="text-gray-600" id="category-text">NS</span>
            </div>
          </div>
          <div class="flex items-center space-x-4 shrink-0" id="color-selection"></div>
        </div>

        <div class="w-full px-4 mb-4">
          <a href="bike-detail.html" class="inline-flex items-center justify-end hover:text-blue-800 transition-colors duration-200">
            View Series page
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>

        <div class="w-full flex justify-end gap-4 pt-4">
          <div class="inline-flex space-x-8 lg:space-x-16 border-b border-gray-200 pl-8 pr-8 overflow-x-auto whitespace-nowrap lg:overflow-x-visible lg:whitespace-normal scrollbar-hide" id="model-tabs-container"></div>
        </div>
      </div>
    </div>
  </section>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      console.log("Initializing Bike Carousel...");

      const bikeData = {
        PULSAR: {
          logo: "./assets/brand-logos/pulsar-logo.png",
          bikes: [
            {
              id: "pulsar-ns200",
              name: "PULSAR NS200",
              image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/ns200v-teaser-images/new-croppedteaser-webp/ns-webp/banner_w_1.webp",
              category: "NS",
              categoryIcon: "./assets/icons/ns.png",
              description: "The Pulsar NS200 is a sporty naked street bike that offers an exciting riding experience with its powerful engine and agile handling.",
              colors: [
                { name: "red", color: "#90020B", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/red/ns200-360-webp/00.png", displayName: "Cocktail Wine Red White" },
                { name: "black", color: "#111116", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-black-webp/00.png", displayName: "Glossy Ebony Black" },
                { name: "white", color: "#ffffff", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/ns-200-white-webp/ns-200-white-webp/00.png", displayName: "Metallic Pearl White" },
                { name: "blue", color: "#233d88", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-grey-webp/00.png", displayName: "Pewter Blue - Grey" },
              ],
            },
            {
              id: "pulsar-220f-abs",
              name: "PULSAR 220F ABS",
              image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/gallery/image6.webp",
              category: "Classic",
              categoryIcon: "./assets/category-icons/pulsar-category-icon.png",
              description: "Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.",
              colors: [
                { name: "red", color: "#90020B", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/ebony-black/00.png", displayName: "Cocktail Wine Red" },
                { name: "purple", color: "#6450D9", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/purple-fury/00.png", displayName: "Purple Fury" },
                { name: "green", color: "#16a34a", image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/pewter-grey-new/00.png", displayName: "Racing Green" },
              ],
            },
            {
              id: "pulsar-150-td",
              name: "PULSAR 150 TD",
              image: "./assets/bikes/pulsar/pulsar_150_td.png",
              category: "Classic",
              categoryIcon: "./assets/icons/classic.png",
              description: "The Pulsar 150 TD combines style with efficiency, offering a perfect balance of performance and fuel economy for everyday riding.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/pulsar/pulsar_150_td_black.png", displayName: "Stealth Black" },
                { name: "red", color: "#dc2626", image: "./assets/bikes/pulsar/pulsar_150_td_red.png", displayName: "Passion Red" },
                { name: "blue", color: "#2563eb", image: "./assets/bikes/pulsar/pulsar_150_td_blue.png", displayName: "Thunder Blue" },
              ],
            },
            {
              id: "pulsar-rs200",
              name: "PULSAR RS200",
              image: "./assets/bikes/pulsar/pulsar_rs200.png",
              category: "Sports",
              categoryIcon: "./assets/icons/sports.png",
              description: "The Pulsar RS200 is a fully-faired sports bike that combines aggressive styling with high performance for an exhilarating ride.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/pulsar/pulsar_rs200_black.png", displayName: "Shadow Black" },
                { name: "yellow", color: "#facc15", image: "./assets/bikes/pulsar/pulsar_rs200_yellow.png", displayName: "Solar Yellow" },
                { name: "red", color: "#dc2626", image: "./assets/bikes/pulsar/pulsar_rs200_red.png", displayName: "Volcanic Red" },
              ],
            },
          ],
        },
        DOMINAR: {
          logo: "./assets/brand-logos/dominar-logo.svg",
          bikes: [
            {
              id: "dominar-400",
              name: "DOMINAR 400",
              image: "./assets/bikes/dominar/dominar_400.png",
              category: "Adventure",
              categoryIcon: "./assets/icons/adventure.png",
              description: "The Dominar 400 is a powerful touring motorcycle designed for long-distance comfort and performance.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/dominar/dominar_400_black.png", displayName: "Obsidian Black" },
                { name: "blue", color: "#2563eb", image: "./assets/bikes/dominar/dominar_400_blue.png", displayName: "Royal Blue" },
                { name: "silver", color: "#6b7280", image: "./assets/bikes/dominar/dominar_400_silver.png", displayName: "Chrome Silver" },
              ],
            },
            {
              id: "dominar-250",
              name: "DOMINAR 250",
              image: "./assets/bikes/dominar/dominar_250.png",
              category: "Touring",
              categoryIcon: "./assets/icons/touring.png",
              description: "The Dominar 250 offers a perfect balance of power and efficiency for urban commuting and weekend getaways.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/dominar/dominar_250_black.png", displayName: "Jet Black" },
                { name: "white", color: "#ffffff", image: "./assets/bikes/dominar/dominar_250_white.png", displayName: "Pearl White" },
                { name: "red", color: "#dc2626", image: "./assets/bikes/dominar/dominar_250_red.png", displayName: "Scarlet Red" },
              ],
            },
          ],
        },
        AVENGERS: {
          logo: "./assets/brand-logos/avengers-logo.svg",
          bikes: [
            {
              id: "avenger-220",
              name: "AVENGER 220",
              image: "./assets/bikes/avengers/avenger_220.png",
              category: "Cruiser",
              categoryIcon: "./assets/icons/cruiser.png",
              description: "The Avenger 220 combines cruiser comfort with sporty performance for an unmatched riding experience.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/avengers/avenger_220_black.png", displayName: "Midnight Black" },
                { name: "silver", color: "#6b7280", image: "./assets/bikes/avengers/avenger_220_silver.png", displayName: "Metallic Silver" },
                { name: "maroon", color: "#7f1d1d", image: "./assets/bikes/avengers/avenger_220_maroon.png", displayName: "Burgundy Red" },
              ],
            },
            {
              id: "avenger-160",
              name: "AVENGER 160",
              image: "./assets/bikes/avengers/avenger_160.png",
              category: "Cruiser",
              categoryIcon: "./assets/icons/cruiser.png",
              description: "The Avenger 160 offers the perfect blend of style and comfort for urban cruising.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/avengers/avenger_160_black.png", displayName: "Phantom Black" },
                { name: "blue", color: "#2563eb", image: "./assets/bikes/avengers/avenger_160_blue.png", displayName: "Ocean Blue" },
                { name: "white", color: "#ffffff", image: "./assets/bikes/avengers/avenger_160_white.png", displayName: "Crystal White" },
              ],
            },
          ],
        },
        DISCOVER: {
          logo: "./assets/brand-logos/discover-logo.svg",
          bikes: [
            {
              id: "discover-125",
              name: "DISCOVER 125",
              image: "./assets/bikes/discover/discover_125.png",
              category: "Commuter",
              categoryIcon: "./assets/icons/commuter.png",
              description: "The Discover 125 is designed for efficient urban commuting with its fuel-efficient engine and comfortable riding position.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/discover/discover_125_black.png", displayName: "Stealth Black" },
                { name: "red", color: "#dc2626", image: "./assets/bikes/discover/discover_125_red.png", displayName: "Racing Red" },
                { name: "blue", color: "#2563eb", image: "./assets/bikes/discover/discover_125_blue.png", displayName: "Electric Blue" },
              ],
            },
            {
              id: "discover-110",
              name: "DISCOVER 110",
              image: "./assets/bikes/discover/discover_110.png",
              category: "Commuter",
              categoryIcon: "./assets/icons/commuter.png",
              description: "The Discover 110 offers excellent fuel efficiency and low maintenance costs for daily commuting.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/discover/discover_110_black.png", displayName: "Midnight Black" },
                { name: "silver", color: "#6b7280", image: "./assets/bikes/discover/discover_110_silver.png", displayName: "Metallic Silver" },
                { name: "green", color: "#16a34a", image: "./assets/bikes/discover/discover_110_green.png", displayName: "Forest Green" },
              ],
            },
          ],
        },
        PLATINA: {
          logo: "./assets/brand-logos/platina-logo.svg",
          bikes: [
            {
              id: "platina-110",
              name: "PLATINA 110",
              image: "./assets/bikes/platina/platina_110.png",
              category: "Economy",
              categoryIcon: "./assets/icons/economy.png",
              description: "The Platina 110 is Bajaj's most fuel-efficient motorcycle, perfect for budget-conscious riders.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/platina/platina_110_black.png", displayName: "Ebony Black" },
                { name: "red", color: "#dc2626", image: "./assets/bikes/platina/platina_110_red.png", displayName: "Racing Red" },
                { name: "silver", color: "#6b7280", image: "./assets/bikes/platina/platina_110_silver.png", displayName: "Platinum Silver" },
              ],
            },
            {
              id: "platina-100",
              name: "PLATINA 100",
              image: "./assets/bikes/platina/platina_100.png",
              category: "Economy",
              categoryIcon: "./assets/icons/economy.png",
              description: "The Platina 100 offers unbeatable fuel efficiency and low maintenance costs for daily commuting.",
              colors: [
                { name: "black", color: "#000000", image: "./assets/bikes/platina/platina_100_black.png", displayName: "Carbon Black" },
                { name: "blue", color: "#2563eb", image: "./assets/bikes/platina/platina_100_blue.png", displayName: "Ocean Blue" },
                { name: "white", color: "#ffffff", image: "./assets/bikes/platina/platina_100_white.png", displayName: "Pearl White" },
              ],
            },
          ],
        },
      };

      let currentBrand = "PULSAR";
      let currentBikeIndex = 0;
      let currentColorIndex = 0;

      const backgroundText = document.getElementById("background-brand-text");
      const bikeTitle = document.getElementById("bike-title");
      const bikeDescription = document.getElementById("bike-description");
      const mainBikeImage = document.getElementById("main-bike-image");
      const brandLogo = document.getElementById("brand-logo");
      const categoryIcon = document.getElementById("category-icon");
      const categoryText = document.getElementById("category-text");
      const colorSelection = document.getElementById("color-selection");
      const brandTabsContainer = document.getElementById("brand-tabs-container");
      const modelTabsContainer = document.getElementById("model-tabs-container");
      const prevBtn = document.getElementById("bike-prev-btn");
      const nextBtn = document.getElementById("bike-next-btn");

      function generateBrandTabs() {
        if (!brandTabsContainer) return;

        brandTabsContainer.innerHTML = "";
        const brands = Object.keys(bikeData);

        brands.forEach((brand, index) => {
          const brandTab = document.createElement("button");
          brandTab.className = `brand-tab text-lg lg:text-xl font-semibold border-b-2 pb-2 ${index === 0 ? "active text-gray-800 border-black" : "text-gray-400 border-transparent hover:text-gray-600"}`;
          brandTab.dataset.brand = brand;
          brandTab.textContent = brand;

          brandTab.addEventListener("click", () => {
            switchBrand(brand);
          });

          brandTabsContainer.appendChild(brandTab);
        });
      }

      function generateModelTabs() {
        if (!modelTabsContainer) return;

        modelTabsContainer.innerHTML = "";
        const brandData = bikeData[currentBrand];
        brandData.bikes.forEach((bike, index) => {
          const modelBtn = document.createElement("button");
          modelBtn.className = `variant-btn px-4 py-2 text-sm font-medium border-b-2 ${index === currentBikeIndex ? "active text-gray-700 border-black" : "text-gray-500 border-transparent hover:text-gray-700"}`;
          modelBtn.dataset.model = bike.id;
          modelBtn.dataset.image = bike.image;
          modelBtn.dataset.name = bike.name;
          modelBtn.dataset.description = bike.description;
          modelBtn.textContent = bike.name;
          modelBtn.addEventListener("click", () => {
            currentBikeIndex = index;
            currentColorIndex = 0;
            updateDisplay();
          });
          modelTabsContainer.appendChild(modelBtn);
        });
      }

      function getColorStyle(colorName) {
        const colorMap = {
          black: "#000000",
          white: "#ffffff",
          red: "#dc2626",
          blue: "#2563eb",
          yellow: "#facc15",
          green: "#16a34a",
          orange: "#ea580c",
          silver: "#6b7280",
          maroon: "#7f1d1d",
          purple: "#6450D9",
        };
        return colorMap[colorName] || "#000000";
      }

      function updateDisplay() {
        const brandData = bikeData[currentBrand];
        const currentBike = brandData.bikes[currentBikeIndex];
        const currentColor = currentBike.colors[currentColorIndex];

        if (backgroundText) {
          backgroundText.textContent = currentBrand;
          if (currentBrand === "PULSAR") {
            backgroundText.classList.add("pulsar-text");
            backgroundText.classList.remove("other-brand-text");
          } else {
            backgroundText.classList.remove("pulsar-text");
            backgroundText.classList.add("other-brand-text");
          }
        }

        if (bikeTitle) bikeTitle.textContent = currentBike.name;
        if (bikeDescription) bikeDescription.textContent = currentBike.description;

        if (mainBikeImage) {
          const imageToUse = currentColor && currentColor.image ? currentColor.image : currentBike.colors[0]?.image || currentBike.image;
          mainBikeImage.src = imageToUse;
          mainBikeImage.alt = currentBike.name;
        }

        if (brandLogo) {
          brandLogo.src = brandData.logo;
          brandLogo.alt = currentBrand + " Logo";
        }
        if (categoryIcon) {
          categoryIcon.src = currentBike.categoryIcon;
          categoryIcon.alt = currentBike.category + " Category";
        }
        if (categoryText) categoryText.textContent = currentBike.category;

        if (colorSelection) {
          colorSelection.innerHTML = "";
          const colorNameDisplay = document.createElement("div");
          colorNameDisplay.className = "text-center";
          colorNameDisplay.innerHTML = `<div class="font-medium text-gray-900" id="current-color-name">${currentColor ? currentColor.displayName : "Black"}</div>`;
          colorSelection.appendChild(colorNameDisplay);

          const colorButtonsContainer = document.createElement("div");
          colorButtonsContainer.className = "flex space-x-2";
          currentBike.colors.forEach((color, index) => {
            const colorBtn = document.createElement("button");
            colorBtn.className = `color-btn w-8 h-8 rounded-full border-2 border-[#326AD2] ${index === currentColorIndex ? "active" : ""}`;
            colorBtn.style.backgroundColor = color.color;
            colorBtn.dataset.color = color.name;
            colorBtn.dataset.colorName = color.displayName;
            colorBtn.dataset.image = color.image;
            colorBtn.title = color.displayName;
            colorBtn.addEventListener("click", () => {
              currentColorIndex = index;
              updateDisplay();
            });
            colorButtonsContainer.appendChild(colorBtn);
          });
          colorSelection.appendChild(colorButtonsContainer);
        }

        brandTabsContainer.querySelectorAll(".brand-tab").forEach((tab) => {
          if (tab.dataset.brand === currentBrand) {
            tab.classList.add("active", "text-gray-800", "border-black");
            tab.classList.remove("text-gray-400", "border-transparent");
          } else {
            tab.classList.remove("active", "text-gray-800", "border-black");
            tab.classList.add("text-gray-400", "border-transparent");
          }
        });

        modelTabsContainer.querySelectorAll(".variant-btn").forEach((btn, index) => {
          if (index === currentBikeIndex) {
            btn.classList.add("active", "text-gray-700", "border-black");
            btn.classList.remove("text-gray-500", "border-transparent");
          } else {
            btn.classList.remove("active", "text-gray-700", "border-black");
            btn.classList.add("text-gray-500", "border-transparent");
          }
        });
      }

      function nextBike() {
        const brandData = bikeData[currentBrand];
        currentBikeIndex = (currentBikeIndex + 1) % brandData.bikes.length;
        currentColorIndex = 0;
        updateDisplay();
      }

      function prevBike() {
        const brandData = bikeData[currentBrand];
        currentBikeIndex = (currentBikeIndex - 1 + brandData.bikes.length) % brandData.bikes.length;
        currentColorIndex = 0;
        updateDisplay();
      }

      function switchBrand(brand) {
        if (bikeData[brand]) {
          currentBrand = brand;
          currentBikeIndex = 0;
          currentColorIndex = 0;
          generateModelTabs();
          updateDisplay();
        }
      }

      window.bikeData = bikeData;

      generateBrandTabs();
      generateModelTabs();

      if (prevBtn) prevBtn.addEventListener("click", prevBike);
      if (nextBtn) nextBtn.addEventListener("click", nextBike);

      updateDisplay();
    });
  </script>
</body>
</html>
