import React, { useEffect, useState } from 'react';
import {
    <PERSON>,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,

    TextField,
    Switch,
} from "@mui/material";
import { styled } from "@mui/material/styles";


const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
}));

const EditAutomatedNotification = (props) => {
    console.log(props.viewDetails)
    const [dialogDetails, setDialogDetails] = useState({
        open: true,
        success: false,
    });

    // const [scheduledDateValue, setScheduledDateValue] = useState("");
    const [validationErrors, setValidationErrors] = useState({});

    const [formData, setFormData] = useState({
        title: props.viewDetails?.title || "",
        message: props.viewDetails?.message || "",
       notify_type: props.viewDetails?.notify_type || "",
        notify_type_id: props.viewDetails?.notify_type_id || "",
        schedule_date: props.viewDetails?.schedule_date || "",
        status: props.viewDetails?.status || 1
    });

    // useEffect(() => {
    //     if (scheduledDateValue !== "") {
    //         const formattedScheduledDate = dayjs(scheduledDateValue).format("YYYY-MM-DD HH:mm");
    //         setFormData({
    //             ...formData,
    //             schedule_date: formattedScheduledDate
    //         });
    //     }
    // }, [scheduledDateValue]);

    useEffect(() => {
        props.sendEdit(dialogDetails, formData);
    }, [dialogDetails]);

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const handleYes = () => {
        let errors = {};

        if (!formData.title.trim()) {
            errors.title = "Title is required.";
        }
        if (!formData.message.trim()) {
            errors.message = "Message is required.";
        }

        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors);
            return;
        } else {
            setDialogDetails({
                ...dialogDetails,
                open: false,
                success: true,
            });
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
        validateField(name, value);
    };

    const validateField = (name, value) => {
        let error = "";

        if (name === "title" && !value.trim()) {
            error = "Title is required.";
        } else if (name === "message" && !value.trim()) {
            error = "Message is required.";
        }

        setValidationErrors((prevErrors) => ({
            ...prevErrors,
            [name]: error
        }));
    };

    return (
        <Dialog
            open={dialogDetails.open}
            onClose={handleClose}
            maxWidth="sm" // Changed to sm for single column
            fullWidth
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <StyledHeaderTitle id="alert-dialog-title">
                {props.isAddNew ? "Add Automated Notification" : "Edit Automated Notification"}
            </StyledHeaderTitle>
            <DialogContent>
                <Box pt={3}>
                    <Box p={3} sx={{ boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" }}>
                        <h5 style={{ margin: 0 }}><i>{"Fields with * are mandatory"}</i></h5>

                        {/* Single Column Layout */}
                        <Box display="flex" flexDirection="column" gap={2}>
                            {!props.isAddNew && (
                                <TextField
                                    label="Notification Type"
                                    name="notification_type"
                                    value={formData.notify_type}
                                    fullWidth
                                    margin="normal"
                                    disabled
                                    InputProps={{
                                        readOnly: true,
                                    }}
                                />
                            )}
                            <TextField
                                required
                                label="Title"
                                name="title"
                                value={formData.title}
                                onChange={handleChange}
                                fullWidth
                                margin="normal"
                                error={!!validationErrors.title}
                                helperText={validationErrors.title}
                            />

                            <TextField
                                required
                                label="Type message here"
                                name="message"
                                value={formData.message}
                                onChange={handleChange}
                                fullWidth
                                margin="normal"
                                multiline
                                rows={3}
                                error={!!validationErrors.message}
                                helperText={validationErrors.message}
                            />

                            <FormControl fullWidth>
                                <Box display="flex" alignItems="center">
                                    <Switch
                                        checked={formData.status}
                                        onChange={(e) =>
                                            setFormData((prev) => ({
                                                ...prev,
                                                status: e.target.checked,
                                            }))
                                        }
                                    />
                                    <span>{formData.status ? "Active" : "Inactive"}</span>
                                </Box>
                            </FormControl>
                        </Box>
                    </Box>
                </Box>
            </DialogContent>
            <DialogActions sx={{ margin: "5px 10px" }}>
                <Button onClick={handleClose} color="error" variant="contained" autoFocus>
                    Cancel
                </Button>
                <Button
                    onClick={handleYes}
                    color="primary"
                    variant="contained"
                    autoFocus
                >
                    {props.isAddNew ? "Save" : "Update"}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default EditAutomatedNotification;