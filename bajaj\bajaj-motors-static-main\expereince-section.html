<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Static Bajaj Experiences Carousel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .expreience-carousel-slide {
            transition: all 0.6s ease;
        }

        .expreience-slide-image {
            transition: transform 0.3s ease;
        }

        .expreience-carousel-slide:hover .expreience-slide-image {
            transform: scale(1.05);
        }

        .expreience-explore-btn {
            transition: all 0.3s ease;
        }

        .expreience-explore-btn:hover {
            transform: translateY(-2px);
        }

        .expreience-nav-button {
            transition: all 0.3s ease;
        }

        .expreience-nav-button:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .expreience-nav-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* Remove unrelated media query */
    </style>
</head>

<body class="">
    <section
        class="bg-gradient-to-br from-blue-50 to-blue-200 min-h-screen flex items-center justify-center p-4 sm:p-6">
        <div class="w-full max-w-3xl mx-auto">
            <h1
                class="text-center text-2xl sm:text-3xl font-bold text-slate-800 mb-8 sm:mb-12 tracking-widest uppercase">
                The Bajaj Experiences
            </h1>

            <div class="relative h-[400px] sm:h-[500px] lg:h-[600px] flex items-center justify-center">
                <div id="carousel" class="relative w-full h-full flex items-center justify-center overflow-hidden">
                    <div class="expreience-carousel-slide absolute w-64 h-80 sm:w-80 sm:h-96 lg:w-[480px] lg:h-[630px] rounded-3xl overflow-hidden shadow-2xl bg-white"
                        data-index="0">
                        <div class="relative w-full h-full overflow-hidden rounded-3xl">
                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=600&fit=crop&crop=center"
                                alt="Red Sports Motorcycle" class="expreience-slide-image w-full h-full object-cover" />
                            <div
                                class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 sm:p-6 flex justify-center items-end hidden">
                                <a href="/blogs.html"
                                    class="expreience-explore-btn bg-black hover:bg-blue-600 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full text-xs sm:text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                    Explore More
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="expreience-carousel-slide absolute w-64 h-80 sm:w-80 sm:h-96 lg:w-[480px] lg:h-[630px] rounded-3xl overflow-hidden shadow-2xl bg-white"
                        data-index="1">
                        <div class="relative w-full h-full overflow-hidden rounded-3xl">
                            <img src="https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=400&h=600&fit=crop&crop=center"
                                alt="Blue Commuter Bike" class="expreience-slide-image w-full h-full object-cover" />
                            <div
                                class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 sm:p-6 flex justify-center items-end hidden">
                                <bautton
                                    class="expreience-explore-btn bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full text-xs sm:text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                    Explore More
                                </bautton>
                            </div>
                        </div>
                    </div>

                    <div class="expreience-carousel-slide absolute w-64 h-80 sm:w-80 sm:h-96 lg:w-[480px] lg:h-[630px] rounded-3xl overflow-hidden shadow-2xl bg-white"
                        data-index="2">
                        <div class="relative w-full h-full overflow-hidden rounded-3xl">
                            <img src="https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=400&h=600&fit=crop&crop=center"
                                alt="Cruiser Motorcycle" class="expreience-slide-image w-full h-full object-cover" />
                            <div
                                class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 sm:p-6 flex justify-center items-end hidden">
                                <a href="/blogs.html"
                                    class="expreience-explore-btn bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full text-xs sm:text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                    Explore More
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <a href="#" id="prevBtn"
                    class="expreience-nav-button absolute top-1/2 -translate-y-1/2 left-2 sm:-left-12 lg:-left-16 bg-white/90 hover:bg-white w-10 h-10 sm:w-12 sm:h-12 rounded-full shadow-lg text-lg sm:text-xl font-bold text-slate-700 z-10 flex items-center justify-center">
                    <i class="fas fa-arrow-left text-black"></i>
                </a>

                <a href="#" id="nextBtn"
                    class="expreience-nav-button absolute top-1/2 -translate-y-1/2 right-2 sm:-right-12 lg:-right-16 bg-white/90 hover:bg-white w-10 h-10 sm:w-12 sm:h-12 rounded-full shadow-lg text-lg sm:text-xl font-bold text-slate-700 z-10 flex items-center justify-center">
                    <i class="fas fa-arrow-right text-black"></i>
                </a>
            </div>
        </div>
    </section>

    <script>
        const slides = document.querySelectorAll(".expreience-carousel-slide");
        const experiencePrevBtn = document.getElementById("prevBtn");
        const experienceNextBtn = document.getElementById("nextBtn");

        let current = 1; // center index

        // Define transform classes for different screen sizes
        const transformClasses = {
            xs: [
                "z-5 opacity-60 transform -translate-x-12 -translate-z-8 rotate-y-8 scale-75", // left
                "z-10 transform translate-x-0 translate-z-0 scale-100", // center
                "z-5 opacity-60 transform translate-x-12 -translate-z-8 -rotate-y-8 scale-75", // right
            ],
            sm: [
                "z-5 opacity-70 transform -translate-x-32 -translate-z-16 rotate-y-10 scale-85", // left
                "z-10 transform translate-x-0 translate-z-0 scale-100", // center
                "z-5 opacity-70 transform translate-x-32 -translate-z-16 -rotate-y-10 scale-85", // right
            ],
            md: [
                "z-5 opacity-75 transform -translate-x-40 -translate-z-20 rotate-y-12 scale-90", // left
                "z-10 transform translate-x-0 translate-z-0 scale-100", // center
                "z-5 opacity-75 transform translate-x-40 -translate-z-20 -rotate-y-12 scale-90", // right
            ],
            lg: [
                "z-5 opacity-80 transform -translate-x-64 -translate-z-32 rotate-y-12 scale-90", // left
                "z-10 transform translate-x-0 translate-z-0 scale-100", // center
                "z-5 opacity-80 transform translate-x-64 -translate-z-32 -rotate-y-12 scale-90", // right
            ],
        };

        // Function to get the current breakpoint's transform classes
        function getCurrentTransformClasses() {
            const screenWidth = window.innerWidth;
            if (screenWidth >= 1024) {
                return transformClasses.lg;
            } else if (screenWidth >= 768) {
                return transformClasses.md;
            } else if (screenWidth >= 480) {
                return transformClasses.sm;
            } else {
                return transformClasses.xs;
            }
        }

        function updateSlides() {
            const currentTransformClasses = getCurrentTransformClasses();
            const screenWidth = window.innerWidth;

            slides.forEach((slide, i) => {
                // Base classes
                let baseClasses = "expreience-carousel-slide absolute rounded-3xl overflow-hidden shadow-2xl bg-white";

                // Apply size based on screen width
                if (screenWidth >= 1024) {
                    baseClasses += " lg:w-[480px] lg:h-[630px]";
                } else if (screenWidth >= 480) {
                    baseClasses += " w-80 h-96";
                } else {
                    baseClasses += " w-64 h-80";
                }

                slide.className = baseClasses;

                const btnWrapper = slide.querySelector(".explore-btn-wrapper");
                btnWrapper.classList.add("hidden");

                const relativeIndex = (i - current + slides.length) % slides.length;

                // Apply transforms
                if (relativeIndex === 0) {
                    slide.className += " " + currentTransformClasses[0]; // Left slide
                } else if (relativeIndex === 1) {
                    slide.className += " " + currentTransformClasses[1]; // Center slide
                    btnWrapper.classList.remove("hidden"); // Show explore button on center
                } else if (relativeIndex === 2) {
                    slide.className += " " + currentTransformClasses[2]; // Right slide
                } else {
                    slide.style.display = "none"; // Hide non-active slides
                }
            });

            // Ensure active slides are visible
            slides.forEach((slide, i) => {
                const relativeIndex = (i - current + slides.length) % slides.length;
                slide.style.display = (relativeIndex <= 2) ? "" : "none";
            });
        }

        experiencePrevBtn.addEventListener("click", () => {
            current = (current - 1 + slides.length) % slides.length;
            updateSlides();
        });

        experienceNextBtn.addEventListener("click", () => {
            current = (current + 1) % slides.length;
            updateSlides();
        });

        // Update on resize
        window.addEventListener("resize", updateSlides);

        updateSlides(); // Initial render
    </script>
</body>

</html>