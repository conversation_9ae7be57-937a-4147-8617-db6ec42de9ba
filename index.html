<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pulsar NS400 360° Viewer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .spritespin-canvas {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain;
            z-index: 10;
            position: relative;
        }

        .background-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom,
                transparent 0%,
                transparent 60%,
                #f8f9fa 60%,
                #f8f9fa 100%);
            z-index: 0;
        }

        .mountain-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 60%;
            background: url('./assets/360/bg.png') no-repeat center top;
            background-size: cover;
            z-index: 1;
        }

        .circle-platform {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: auto;
            z-index: 2;
            opacity: 0.8;
        }

        .bike-container {
            position: relative;
            z-index: 10;
        }

        .price-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .loading-overlay {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
        }
    </style>
</head>

<body class="relative bg-gray-50 min-h-screen flex flex-col items-center justify-center overflow-hidden">

    <!-- Background layers -->
    <div class="background-container"></div>
    <div class="mountain-background"></div>

    <!-- 360 Circle Platform -->
    <img src="./assets/360/360.png"
         alt="360 platform"
         class="circle-platform">

    <div class="relative z-10 w-full max-w-[900px] mx-auto p-4 text-center">

        <!-- 360 Viewer Container -->
        <div id="bike-viewer" class="bike-container relative w-full h-[500px] mx-auto overflow-visible loading">

            <div class="loading-overlay absolute inset-0 flex items-center justify-center text-center font-sans text-gray-700 rounded-lg">
                <div>
                    <div class="w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-2">
                    </div>
                    <div id="loading-text">Loading 360° View...</div>
                    <div class="w-[100px] h-1 bg-gray-200 mt-2 mx-auto">
                        <div class="progress-bar h-full bg-blue-500 w-0 transition-all duration-300"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Price and Availability -->
        <div class="price-section text-center text-black font-sans">
            <p class="text-2xl font-bold mb-2">Starts at 4,98,900</p>
            <p class="text-sm text-gray-600">Available at all authorized HH Bajaj<br>showrooms across Nepal</p>
        </div>
    </div>

    <!-- Include jQuery and SpriteSpin -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/spritespin@4.1.0/release/spritespin.min.js"></script>

    <script>
        (function ($) {
            $(document).ready(function () {

                // Use local images from assets/360 folder
                const images = [];
                for (let i = 1; i <= 13; i++) {
                    images.push(`./assets/360/${i}.png`);
                }

                const $container = $('#bike-viewer');
                const $progressBar = $('.progress-bar');
                const $loadingText = $('#loading-text');
                const $loadingOverlay = $('.loading-overlay');
                let loadedImages = 0;

                function showError(message) {
                    $loadingOverlay.html(`
                        <div class="text-center font-sans text-red-500">
                            <div class="text-2xl mb-2">⚠️</div>
                            <div>${message}</div>
                            <button class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="location.reload()">Retry</button>
                        </div>
                    `);
                }

                function preloadImages(callback) {
                    images.forEach(src => {
                        const img = new Image();
                        img.src = src;
                        img.onload = () => {
                            loadedImages++;
                            updateProgress(callback);
                        };
                        img.onerror = () => {
                            console.error(`Failed to load ${src}`);
                            loadedImages++;
                            updateProgress(callback);
                        };
                    });
                }

                function updateProgress(callback) {
                    const percent = (loadedImages / images.length) * 100;
                    $progressBar.css('width', percent + '%');
                    if (loadedImages === images.length) {
                        callback();
                    }
                }

                preloadImages(() => {
                    $loadingText.text('Initializing viewer...');

                    // Hide loading overlay and initialize SpriteSpin
                    $loadingOverlay.fadeOut(500, () => {
                        $container.removeClass('loading');

                        $container.spritespin({
                            source: images,
                            width: 631,
                            height: 409,
                            frameTime: 120,
                            animate: false,
                            sense: -1,
                            responsive: true,
                            loop: true,
                            plugins: ['360', 'drag'],
                            onLoad: () => {
                                console.log('SpriteSpin ready');
                                // Add subtle animation on load
                                $container.find('.spritespin-canvas').css({
                                    'filter': 'drop-shadow(0 10px 30px rgba(0,0,0,0.3))'
                                });
                            },
                            onError: () => showError('Failed to initialize 360° viewer')
                        });
                    });
                });

            });
        })(jQuery);
    </script>
</body>

</html>
