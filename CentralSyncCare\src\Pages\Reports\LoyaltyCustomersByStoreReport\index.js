import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  CircularProgress,
  styled,
  Chip,
  Snackbar,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import httpclient from '../../../Utils';
import MuiAlert from "@mui/material/Alert";

const Header = styled("div")(({ theme }) => ({
  "& h1": {
    color: theme.palette.primary.dark,
    margin: "0",
  },
}));

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});


export default function LoyaltyCustomersByStoreReport() {
  const [data, setData] = useState([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messageState, setMessageState] = useState("success");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [activeFilters, setActiveFilters] = useState({});

  const fetchData = async (filters = {}) => {
    try {
      setLoading(true);
      let url = 'request-response?requestName=lightspeed/report/loyalty-customer-report';

      if (filters.startDate) {
        url += `&start_date=${filters.startDate}`;
      }
      if (filters.endDate) {
        url += `&end_date=${filters.endDate}`;
      }

      const response = await httpclient.get(url);

      if (response?.data?.status === 200) {
        const { results, totalCustomers } = response.data.data;
        setData(results);
        setTotalCustomers(totalCustomers);
        setMessageState("success");
        setMessage(response.data?.message || "Customer Report fetched successfully");
        setOpen(true);  
      } else {
        setData([]);
        setTotalCustomers(0);
        setMessage(response.data?.message || "An error occurred");
        setMessageState("error");
        setOpen(true);
      }
    } catch (error) {
      console.error('API error:', error);
      setData([]);
      setTotalCustomers(0);
      setMessage(error?.response?.data?.message || "An error occurred");
      setMessageState("error");
      setOpen(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleFilter = () => {
    if (!startDate || !endDate) {
      setMessageState("error");
      setMessage("Please select both Start Date and End Date.");
      setOpen(true); // Show Snackbar
      return;
    }

    const formattedStart = dayjs(startDate).format('YYYY-MM-DD');
    const formattedEnd = dayjs(endDate).format('YYYY-MM-DD');

    const filters = {
      startDate: formattedStart,
      endDate: formattedEnd,
    };

    fetchData(filters);
    setActiveFilters(filters);
  };


  const handleRemoveFilter = (key) => {
    const updatedFilters = { ...activeFilters };
    delete updatedFilters[key];

    if (key === 'startDate') setStartDate(null);
    if (key === 'endDate') setEndDate(null);

    setActiveFilters(updatedFilters);
    fetchData(updatedFilters);
  };

  const handleExport = () => {
    const csvContent = [
      ['S.N', 'Store', 'Total Customers'],
      ...data.map((item, index) => [index + 1, item.manualStoreCode.trim(), item.total]),
      ['', 'Total Customers', totalCustomers],
    ]
      .map((row) => row.join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = 'loyalty_customers_by_store.csv';
    link.click();
  };

  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpen(false);
  };

  return (
    <Box>
      <Typography variant="h4" fontWeight="bold" marginBottom={"18px"} gutterBottom>
        Loyalty Customers By Store
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            label="Start Date"
            value={startDate}
            onChange={(newValue) => setStartDate(newValue)}
            slotProps={{ textField: { size: 'small' } }}
          />
          <DatePicker
            label="End Date"
            value={endDate}
            onChange={(newValue) => setEndDate(newValue)}
            slotProps={{ textField: { size: 'small' } }}
          />
        </LocalizationProvider>

        <Button variant="contained" onClick={handleFilter}>
          Filter
        </Button>
        <Button variant="contained" onClick={handleExport}>
          Export as CSV
        </Button>
      </Box>

      {Object.keys(activeFilters).length > 0 && (
        <Box sx={{ display: 'flex', gap: 1, mb: 4, flexWrap: 'wrap' }}>
          {activeFilters.startDate && (
            <Chip
              label={`Start Date: ${activeFilters.startDate}`}
              onDelete={() => handleRemoveFilter('startDate')}
            />
          )}
          {activeFilters.endDate && (
            <Chip
              label={`End Date: ${activeFilters.endDate}`}
              onDelete={() => handleRemoveFilter('endDate')}
            />
          )}
        </Box>
      )}

      {loading ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead sx={{ backgroundColor: '#f3f4f6' }}>
              <TableRow>
                <TableCell><strong>S.N</strong></TableCell>
                <TableCell><strong>Store</strong></TableCell>
                <TableCell><strong>Total Customers</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((item, index) => (
                <TableRow key={item.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{item.manualStoreCode.trim()}</TableCell>
                  <TableCell>{item.total}</TableCell>
                </TableRow>
              ))}
              <TableRow>
                <TableCell />
                <TableCell><strong>Total Customers</strong></TableCell>
                <TableCell><strong>{totalCustomers}</strong></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Snackbar
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={open}
        onClose={handleClose}
      >
        <Alert
          onClose={handleClose}
          severity={messageState}
          sx={{ width: "100%" }}
        >
          {message}
        </Alert>
      </Snackbar>
    </Box>
  );
}
