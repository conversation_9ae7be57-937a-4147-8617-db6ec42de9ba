<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bike 360 Viewer</title>
    <!-- Include Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom keyframes for spinner animation */
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Ensure canvas fits container and add subtle platform ring */
        .spritespin-canvas {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain;
        }

        .platform-ring {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 20px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
            border-radius: 50%;
            filter: blur(2px);
        }
    </style>
</head>

<body class="bg-white min-h-screen flex items-center justify-center">
    <!-- Background image using <img> tag -->
    <img src="https://images.unsplash.com/photo-1612643909886-acd85e9d37bc?q=80&w=1170&auto=format&fit=crop" alt="Mountain background"
        class="absolute inset-0 w-full h-full object-cover opacity-30 z-0 pointer-events-none">
    <div class="relative z-10 w-full max-w-[800px] mx-auto p-4">
        <!-- 360 Viewer Container -->
        <div id="bike-viewer" class="relative w-full h-[500px] mx-auto overflow-hidden loading">
            <div
                class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-sans text-gray-700">
                <div class="w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-2">
                </div>
                <div id="loading-text">Loading 360° View...</div>
                <div class="w-[100px] h-1 bg-gray-200 mt-2">
                    <div class="progress-bar h-full bg-blue-500 w-0 transition-all duration-300"></div>
                </div>
            </div>
            <!-- Subtle platform ring -->
            <div class="platform-ring"></div>
        </div>
        <!-- 360° Label (integrated into arc) -->
        <div
            class="absolute -bottom-4 left-1/2 -translate-x-1/2 text-gray-500 text-sm font-sans rotate-[-15deg] origin-bottom">
            360°</div>
        <!-- Pricing and Info -->
        <div class="text-center mt-6 text-gray-700 font-sans">
            <p class="text-xl font-bold">Starts at 4,98,000</p>
            <p class="text-sm">Available at all authorized HH Bajaj showrooms across Nepal</p>
        </div>
    </div>

    <!-- Include jQuery and SpriteSpin via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/spritespin@4.1.0/release/spritespin.min.js"></script>
    <script>
        (function ($) {
            $(document).ready(function () {
                // Verify jQuery and SpriteSpin are loaded
                if (typeof $ === 'undefined') {
                    showError('jQuery failed to load');
                    return;
                }
                if (typeof $.fn.spritespin === 'undefined') {
                    showError('SpriteSpin failed to load');
                    return;
                }

                const images = [
                    'assets/360/1.png',
                    'assets/360/2.png',
                    'assets/360/3.png',
                    'assets/360/4.png',
                    'assets/360/5.png',
                    'assets/360/6.png',
                    'assets/360/7.png',
                    'assets/360/8.png',
                    'assets/360/9.png',
                    'assets/360/10.png',
                    'assets/360/11.png',
                    'assets/360/12.png',
                    'assets/360/13.png'
                ];

                let loadedImages = 0;
                const totalImages = images.length;
                const $container = $('#bike-viewer');
                const $progressBar = $('.progress-bar');
                const $loadingText = $('#loading-text');

                // Error handling function
                function showError(message) {
                    $container.removeClass('loading').addClass('error').html(`
                        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-sans text-red-500">
                            <div class="text-2xl mb-2">⚠️</div>
                            <div>${message}</div>
                            <button class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="location.reload()">Retry</button>
                        </div>
                    `);
                    console.error(message);
                }

                // Preload images with timeout and detailed logging
                function preloadImages(callback) {
                    let loadAttempts = 0;
                    const maxAttempts = 30; // 30 seconds timeout
                    const checkInterval = 1000; // Check every 1 second

                    images.forEach((src, index) => {
                        const img = new Image();
                        img.src = src;
                        img.onload = () => {
                            loadedImages++;
                            updateProgress();
                            console.log(`Image loaded: ${src}`);
                        };
                        img.onerror = () => {
                            console.error(`Failed to load image: ${src}`);
                            loadedImages++; // Count as loaded to avoid infinite loop
                            updateProgress();
                        };
                    });

                    function updateProgress() {
                        const progress = (loadedImages / totalImages) * 100;
                        $progressBar.css('width', progress + '%');
                        if (loadedImages === totalImages) {
                            clearInterval(timeoutId);
                            callback();
                        } else if (loadAttempts >= maxAttempts) {
                            clearInterval(timeoutId);
                            showError('Image loading timed out after 30 seconds');
                        }
                        loadAttempts++;
                    }

                    const timeoutId = setInterval(updateProgress, checkInterval);
                }

                // Initialize SpriteSpin after images are loaded
                preloadImages(() => {
                    $loadingText.text('Initializing viewer...'); // Update text during initialization
                    $container.removeClass('loading').empty(); // Clear loading indicator
                    $container.spritespin({
                        source: images,
                        width: 800,
                        height: 500,
                        frameTime: 100, // For smooth manual transitions
                        animate: false, // Disable auto-rotation
                        sense: -1, // Reverse direction for intuitive drag
                        responsive: true, // Mobile-friendly
                        loop: true, // Continuous rotation
                        plugins: ['360', 'drag'], // Enable drag interaction
                        onFrame: function () {
                            // Optional: Haptic feedback for touch devices
                            if ('vibrate' in navigator) {
                                navigator.vibrate(10);
                            }
                        },
                        onLoad: function () {
                            console.log('SpriteSpin loaded successfully');
                        },
                        onError: function () {
                            showError('Error initializing SpriteSpin');
                        }
                    }).bind('onError', function () {
                        showError('SpriteSpin initialization failed');
                    });
                });
            });
        })(jQuery);
    </script>
</body>

</html>