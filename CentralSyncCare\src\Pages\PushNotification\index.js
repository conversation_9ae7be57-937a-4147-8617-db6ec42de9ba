import React, { useEffect, useState } from "react";

import {
    Box,
    Button,
    Card,
    Collapse,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    styled,
    TextField,
    Snackbar,
} from "@mui/material";
import { Add, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FilterList } from "@mui/icons-material";
import httpclient from "../../Utils";
import { useNavigate } from "react-router-dom";
import MuiAlert from "@mui/material/Alert";
import TableComponent from "../../Components/TableComponent";
import EditDialogRules from "../../Components/EditDialogRules";
import DeleteDialog from "../../Components/DeleteDialog";
import useTokenRefresh from "../../Hooks/useTokenRefresh";
import Footer from "../../Components/Footer";
import EditPushNotification from "../../Components/EditPushNotification";
import ViewPushNotification from "./ViewPushNotification";

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const login = localStorage.getItem("login");
const loginData = JSON.parse(login);


const columns = [
    { id: "sn", name: "SN" },
    { id: "title", name: "Title" },
    { id: "message_send_by_type", name: "Message Send By" },
    { id: "schedule_date", name: "Scheduled Date" },
    { id: "is_created_schedule", name: "Push To LiteCard?" },
    { id: "view", name: "View" },
    { id: "actions", name: "Actions" },
];


const superOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "deactivate", name: "Deactivate", action: "handleDeactivate" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
    { id: "delete", name: "Delete", action: "handleDelete" },
];

const adminOptions = [
    { id: "edit", name: "Edit", action: "handleEdit" },
    { id: "reset", name: "Reset Password", action: "handleResetPassword" },
]

const FilteredBox = styled(Box)(({ theme }) => ({
    background: "#f9f9f9",
    padding: "5px 10px",
    borderRadius: "5px",
    "& p": {
        margin: "0",
        marginRight: "10px",
        display: "inline-block",
        background: "#dedede",
        borderRadius: "10px",
        padding: "2px 5px",
    },
    "& svg": {
        fontSize: "15px",
        cursor: "pointer",
        position: "relative",
        top: "3px",
        background: theme.palette.primary.dark,
        color: "#fff",
        borderRadius: "50%",
        padding: "2px",
        marginLeft: "2px",
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const configRowPerPage = JSON.parse(localStorage.getItem("configRowPerPage"));



const PushNotification = (props) => {
    console.log(props)

    const { getTokenRefreshed: refresh, open: tokenOpen, overlay: overlay, setOpen: setTokenOpen, message: tokenMessage, messageState: tokenMessageState } = useTokenRefresh();

    const navigate = useNavigate();
    const [openResetDialog, setOpenResetDialog] = useState(false);
    const [view, setView] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [menuList, setMenuList] = useState([]);
    const [openActiveDialog, setOpenActiveDialog] = useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [openViewDialog, setOpenViewDialog] = useState(false);
    const [openPushNotificationDialog, setOpenPushNotificationDialog] = useState(false);
    const [rows, setRows] = useState([]);
    const [modulePermissionList, setModulePermissionList] = useState([]);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");

    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");
    const [page, setPage] = useState(1);
    const [from, setFrom] = useState(1);
    const [to, setTo] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );

    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage && configRowPerPage
            ? configRowPerPage && configRowPerPage
            : 20
    );
    const [total, setTotal] = useState("");
    const [filterOpen, setFilterOpen] = useState(false);

    const [filterData, setFilterData] = useState({
        notification_id: "",
        notification_title: "",
        is_created_schedule: "",
        startDate: "",
        endDate: "",
        remove: false,
    });

    const [submittedData, setSubmittedData] = useState({
        notification_id: "",
        notification_title: "",
        is_created_schedule: "",
        startDate: "",
        endDate: "",
        submit: false,
    });

    // useEffect(() => {
    //   getAllPushNotification();
    // }, []);

    useEffect(() => {
        if (
            filterData.notification_id === "" &&
            filterData.notification_title === "" &&
            filterData.is_created_schedule === "" &&
            filterData.startDate === "" &&
            filterData.endDate === ""
        ) {
            setSubmittedData({
                ...submittedData,
                submit: false,
            });
        }
        if (filterData.notification_id === " ") filterData.notification_id = "";
        if (filterData.notification_title === " ") filterData.notification_title = "";
        if (filterData.is_created_schedule === " ") filterData.is_created_schedule = "";
        if (filterData.startDate === " ") filterData.startDate = "";
        if (filterData.endDate === " ") filterData.endDate = "";

        filterData.remove === true && handleFilter();
    }, [filterData]);

    useEffect(() => {
        let userStorage = JSON.parse(localStorage.getItem("push_notification_filter"));
        userStorage !== null && setFilterData(userStorage);

        userStorage == null
            ? getAllPushNotification()
            : userStorage.notification_id == "" &&
                userStorage.notification_title == "" &&
                userStorage.is_created_schedule == "" &&
                userStorage.startDate == "" &&
                userStorage.endDate == "" &&


                userStorage.removed == false
                ? getAllPushNotification()
                : console.log("users!");
    }, []);

    const getAllPushNotification = () => {
        setLoading(true);
        httpclient.get(`request-response?requestName=lightspeed/push-notification&pagination=${rowsPerPage}`).then(({ data }) => {
            if (data.success) {
                setRows(data.data);
                setModulePermissionList(data.modulePermissionList);
                setTotal(data.meta.total);
                setRowsPerPage(parseInt(data.meta.per_page));
                setPage(data.meta.current_page);
                setFrom(data.meta.from);
                setTo(data.meta.to);
                setLoading(false);
            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");
                setLoading(false);
            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
                setLoading(false);

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
                setLoading(false);
            }
        })

    };

    const hadleFilterOpen = () => {
        setFilterOpen((prev) => !prev);
    };

    const handleChangeFilter = (e) => {
        const { name, value } = e.target;
        setFilterData({
            ...filterData,
            [name]: value,
            remove: false,
        });
    };

    const handleFilter = () => {
        setSubmittedData({
            ...submittedData,
            notification_id: filterData.notification_id,
            notification_title: filterData.notification_title,
            is_created_schedule: filterData.is_created_schedule,
            startDate: filterData.startDate,
            endDate: filterData.endDate,

            submit: true,
        });
        filterData.remove = true;
        localStorage.setItem("push_notification_filter", JSON.stringify(filterData));
        setLoading(true);
        if (
            filterData.notification_id ||
            filterData.notification_title ||
            filterData.is_created_schedule ||
            filterData.startDate ||
            filterData.endDate
        ) {
            httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&filters[notification_id][$eq]=${filterData.notification_id}&filters[title][$contains]=${filterData.notification_title
                    }&filters[is_created_schedule][$eq]=${filterData.is_created_schedule
                    }&filters[schedule_date][$between][0]=${filterData.startDate
                    }&filters[schedule_date][$between][1]=${filterData.endDate
                    }&pagination=${rowsPerPage}&page=${1}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setModulePermissionList(data.modulePermissionList);
                        setTotal(data.meta.total);
                        setRowsPerPage(data.meta.per_page);
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })

        } else {
            getAllPushNotification();
        }
    };

    const handleRemove = (data) => {
        if (data === "startDate") {
            setFilterData({
                ...filterData,
                startDate: "",
                endDate: "",
                remove: true,
            });
            setSubmittedData({
                ...submittedData,
                startDate: "",
                endDate: "",
            });
        } else {
            setFilterData({
                ...filterData,
                [data]: "",
                remove: true,
            });

            setSubmittedData({
                ...submittedData,
                [data]: "",
            });
        }
    };

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&filters[notification_id][$eq]=${filterData.notification_id
                    }&filters[title][$contains]=${filterData.notification_title
                    }&filters[is_created_schedule][$eq]=${filterData.is_created_schedule
                    }&filters[schedule_date][$between][0]=${filterData.startDate
                    }&filters[schedule_date][$between][1]=${filterData.endDate
                    }&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&sort[0]=${column}:${!direction ? "asc" : "desc"
                    }&pagination=${rowsPerPage}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangePage = (e, page) => {
        setLoading(true);
        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&filters[notification_id][$eq]=${filterData.notification_id
                    }&filters[title][$contains]=${filterData.notification_title
                    }&filters[is_created_schedule][$eq]=${filterData.is_created_schedule
                    }&filters[schedule_date][$between][0]=${filterData.startDate
                    }&filters[schedule_date][$between][1]=${filterData.endDate
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&pagination=${rowsPerPage}&page=${page}`
                )
                .then(({ data }) => {
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setLoading(true);

        localStorage.setItem("configRowPerPage", event.target.value);

        submittedData.submit
            ? httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&filters[notification_id][$eq]=${filterData.notification_id
                    }&filters[title][$contains]=${filterData.notification_title
                    }&filters[is_created_schedule][$eq]=${filterData.is_created_schedule
                    }&filters[schedule_date][$between][0]=${filterData.startDate
                    }&filters[schedule_date][$between][1]=${filterData.endDate
                    }${currentColumn ? `&sort[0]=${currentColumn}:${direction ? "asc" : "desc"}` : ''
                    }&pagination=${+event.target.value}&page=${page}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setPage(data.meta.current_page);
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
            : httpclient
                .get(
                    `request-response?requestName=lightspeed/push-notification&pagination=${+event
                        .target.value}&page=${1}`
                )
                .then(({ data }) => {
                    setLoading(true);
                    if (data.success) {
                        setRows(data.data);
                        setTotal(data.meta.total);
                        setRowsPerPage(parseInt(data.meta.per_page));
                        setFrom(data.meta.from);
                        setTo(data.meta.to);
                        setPage(data.meta.current_page);
                        setLoading(false);
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
    };


    const handleAddNew = () => {
        setOpenEditDialog(true)
    };

    const handleEdit = (row) => {
        setOpenEditDialog(true)
        setViewDetails(row);
    };

    const handleView = (row) => {
        setOpenPushNotificationDialog(true)
        setViewDetails(row);
    };

    const handleClosePushNotification = () => {
        setOpenPushNotificationDialog(false)
        setViewDetails({});
    };

    const handleCloseView = () => {
        setOpenViewDialog(false)
        setViewDetails({});
    };

    const sendEdit = (call, formData) => {
        if (call.open === false) {

            setOpenEditDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            viewDetails.id ? (
                httpclient
                    .put(`request-response?requestName=lightspeed/push-notification/${viewDetails.id}`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllPushNotification();
                        }
                        else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }
                    })
            ) :
                httpclient
                    .post(`request-response?requestName=lightspeed/push-notification`, formData)
                    .then(({ data }) => {
                        if (data.status === 200) {
                            setOpen(true);
                            setMessageState("success");
                            setMessage(data.message);
                            setOpenActiveDialog(false);
                            setViewDetails({});
                            getAllPushNotification();
                        } else {
                            setOpen(true);
                            setMessage(data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    }).catch((err) => {
                        if (err.response.status === 401) {
                            refresh();
                            setOpen(tokenOpen);
                            setMessage(tokenMessage);
                            setMessageState("error");
                        } else if (err.response.status === 422) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);
                        } else if (err.response.status === 400) {
                            const errorMessages = Object.values(err.response.data.errors).flat();
                            setOpen(true);
                            setMessage(errorMessages);
                            setMessageState("error");
                            setLoading(false);

                        } else {
                            setOpen(true);
                            setMessage(err.response.data.message);
                            setMessageState("error");
                            setLoading(false);
                        }

                    })
        }
    };


    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row)
    };

    const sendDelete = (call) => {
        if (call.open === false) {
            setOpenDeleteDialog(false)
            setViewDetails({})
        }
        if (call.success === true) {
            httpclient
                .delete(`request-response?requestName=lightspeed/push-notification/${viewDetails.id}`)
                .then(({ data }) => {
                    if (data.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(data.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getAllPushNotification();
                    } else {
                        setOpen(true);
                        setMessage(data.message);
                        setMessageState("error");
                        setLoading(false);
                    }

                }).catch((err) => {
                    if (err.response.status === 401) {
                        refresh();
                        setOpen(tokenOpen);
                        setMessage(tokenMessage);
                        setMessageState("error");
                    } else if (err.response.status === 422) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);
                    } else if (err.response.status === 400) {
                        const errorMessages = Object.values(err.response.data.errors).flat();
                        setOpen(true);
                        setMessage(errorMessages);
                        setMessageState("error");
                        setLoading(false);

                    } else {
                        setOpen(true);
                        setMessage(err.response.data.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                })
        }
    }

    // Handle API call to update row status
    const updateRowStatus = (rowId) => {
        setRowLoading((prev) => ({ ...prev, [rowId.id]: true }));    
        httpclient
            .post(`request-response?requestName=lightspeed/push-notification/status/${rowId.id}`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    setViewDetails({});
                    getAllPushNotification();
                } else {
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.error || data.message);
                }
            }).catch((err) => {
                if (err?.response?.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err?.response?.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                } else if (err?.response?.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));

                } else {
                    setOpen(true);
                    setMessage(err?.response?.data?.message);
                    setMessageState("error");
                    setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
                }
            }).finally(() => {
                setRowLoading((prev) => ({ ...prev, [rowId.id]: false }));
            });
    };


    const currentChange = (value, row) => {

        if (value === "allow_update") {
            handleEdit(row);
        }

        if (value === "allow_delete") {
            handleDelete(row);
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
        setTokenOpen(false);
    };

    return (
        <div>
            <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                    <Header>
                        <h1>List Push Notification</h1>
                    </Header>
                </Grid>
                <Grid
                    item
                    md={4}
                    xs={12}
                    display="flex"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <Button color="primary" variant="contained" onClick={hadleFilterOpen}>
                        Filter <FilterList style={{ marginLeft: "5px" }} fontSize="small" />
                    </Button>

                    {props?.permissions?.some((pre) => pre.name === "allow_create" && pre.status === 1) &&
                        <AddButton
                            color="primary"
                            variant="contained"
                            onClick={handleAddNew}
                        >
                            <Add style={{ marginRight: "5px" }} fontSize="small" /> Add Push Notification
                        </AddButton>
                    }
                </Grid>

                {/* Filter */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>
                                    {/* <Grid item xs={12} md={4}>
                                        <InputLabel>Project ID</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="notification_id"
                                            value={filterData.notification_id}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid> */}
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Title</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="notification_title"
                                            value={filterData.notification_title}
                                            onChange={handleChangeFilter}
                                            onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Push To LiteCard?</InputLabel>
                                        <FormControl fullWidth>
                                            <Select
                                                name="is_created_schedule"
                                                value={filterData.is_created_schedule}
                                                onChange={handleChangeFilter}
                                                onKeyDown={e => { if (e.key === "Enter") handleFilter() }}
                                            >
                                                <MenuItem value={""}>Select</MenuItem>
                                                <MenuItem value={"1"}>Yes</MenuItem>
                                                <MenuItem value={"0"}>No</MenuItem>

                                            </Select>
                                        </FormControl>



                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <InputLabel>Start Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="startDate"
                                            type="date"
                                            value={filterData.startDate}
                                            onChange={(e) => handleChangeFilter(e)}
                                            fullWidth
                                            InputLabelProps={{
                                                shrink: true,
                                            }}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={4}>
                                        <InputLabel>End Date</InputLabel>
                                        <TextField
                                            variant="outlined"
                                            name="endDate"
                                            type="date"
                                            value={filterData.endDate}
                                            onChange={(e) => handleChangeFilter(e)}
                                            fullWidth
                                            InputLabelProps={{
                                                shrink: true,
                                            }}
                                        />
                                    </Grid>



                                    <Grid item xs={12}>
                                        <Box textAlign={"right"}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter{" "}
                                                <ArrowForward
                                                    fontSize="small"
                                                    style={{ marginLeft: "5px" }}
                                                />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {submittedData.notification_id ||
                    submittedData.notification_title ||
                    submittedData.is_created_schedule ||
                    submittedData.startDate ? (
                    <Grid item xs={12}>
                        <FilteredBox>
                            <span>Filtered: </span>
                            {submittedData.notification_id && (
                                <p>
                                    <span>Rule ID: {submittedData.notification_id}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("notification_id")}
                                    />
                                </p>
                            )}
                            {submittedData.notification_title && (
                                <p>
                                    <span>Title: {submittedData.notification_title}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("notification_title")}
                                    />
                                </p>
                            )}
                            {submittedData.is_created_schedule && (
                                <p>
                                    <span>Push To LiteCard?: {submittedData.is_created_schedule === "1" ? "Yes" : "No"}</span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("is_created_schedule")}
                                    />
                                </p>
                            )}
                            {(submittedData.startDate || submittedData.endDate) && (
                                <p>
                                    <span>
                                        Scheduled Date Range: {submittedData.startDate} -{" "}
                                        {submittedData.endDate}
                                    </span>
                                    <Close
                                        fontSize="small"
                                        onClick={() => handleRemove("startDate")}
                                    />
                                </p>
                            )}

                        </FilteredBox>
                    </Grid>
                ) : (
                    <Box></Box>
                )}
                {/* Filter */}

                <Grid item xs={12}>
                    <TableComponent
                        name={"Notification"}
                        columns={columns}
                        rows={rows}
                        sort={true}
                        handleSort={handleSort}
                        handleNewView={handleView}
                        updateRowStatus={updateRowStatus}
                        setRowLoading={setRowLoading}
                        rowLoading={rowLoading}
                        props={props}
                        options={props?.permissions}
                        currentChange={currentChange}
                        loading={loading}
                        direction={direction}
                        currentColumn={currentColumn}
                        handleChangeRowsPerPage={handleChangeRowsPerPage}
                        handleChangePage={handleChangePage}
                        page={page}
                        total={total && total}
                        fromTable={from}
                        toTable={to}
                        rowsPerPage={rowsPerPage}
                    />
                </Grid>
                <Footer overlay={overlay || props.overlayNew} />
            </Grid>



            {openDeleteDialog && <DeleteDialog name={"Push Notification"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditPushNotification
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                />
            )}

            {openPushNotificationDialog && (
                <ViewPushNotification
                    viewDetails={viewDetails}
                    handleClosePushNotification={handleClosePushNotification}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open || tokenOpen}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState || tokenMessageState}
                    sx={{ width: "100%" }}
                >
                    {message || tokenMessage}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default PushNotification;
