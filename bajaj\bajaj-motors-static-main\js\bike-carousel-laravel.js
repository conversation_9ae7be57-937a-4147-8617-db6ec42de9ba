/**
 * BIKE CAROUSEL JAVASCRIPT - LARAVEL READY
 * 
 * This file contains all the DOM manipulation logic for the bike carousel.
 * The data structure is expected to be provided externally via window.bikeData.
 * 
 * LARAVEL INTEGRATION INSTRUCTIONS:
 * 
 * 1. In your Laravel Controller:
 * ================================
 * 
 * $bikeData = [
 *     'PULSAR' => [
 *         'logo' => asset('assets/brand-logos/pulsar-logo.png'),
 *         'category' => 'Sports',
 *         'categoryIcon' => asset('assets/icons/sports.png'),
 *         'bikes' => [
 *             [
 *                 'id' => 'pulsar-220f-abs',
 *                 'name' => 'PULSAR 220F ABS',
 *                 'image' => asset('assets/bikes/pulsar/pulsar_220f_abs.png'),
 *                 'description' => 'Experience the perfect blend...',
 *                 'colors' => [
 *                     ['name' => 'black', 'color' => '#000000'],
 *                     ['name' => 'yellow', 'color' => '#facc15'],
 *                 ]
 *             ],
 *         ]
 *     ],
 * ];
 * 
 * return view('your-view', compact('bikeData'));
 * 
 * 2. In your Blade template:
 * ==========================
 * 
 * <script>
 *     window.bikeData = @json($bikeData);
 * </script>
 * <script src="{{ asset('js/bike-carousel-laravel.js') }}"></script>
 * 
 */

class BikeCarousel {
  constructor(bikeData) {
    this.bikeData = bikeData;
    this.currentBrand = Object.keys(bikeData)[0] || 'PULSAR';
    this.currentBikeIndex = 0;
    this.currentColorIndex = 0;
    
    // DOM elements
    this.elements = {
      backgroundText: document.getElementById('background-brand-text'),
      bikeTitle: document.getElementById('bike-title'),
      bikeDescription: document.getElementById('bike-description'),
      mainBikeImage: document.getElementById('main-bike-image'),
      brandLogo: document.getElementById('brand-logo'),
      categoryIcon: document.getElementById('category-icon'),
      categoryText: document.getElementById('category-text'),
      colorSelection: document.getElementById('color-selection'),
      brandTabs: document.querySelectorAll('.brand-tab'),
      variantBtns: document.querySelectorAll('.variant-btn'),
      modelGroups: document.querySelectorAll('.model-group'),
      prevBtn: document.getElementById('bike-prev-btn'),
      nextBtn: document.getElementById('bike-next-btn')
    };
    
    this.init();
  }
  
  init() {
    console.log('🚀 Initializing Bike Carousel...');
    console.log('📊 Available brands:', Object.keys(this.bikeData));
    
    this.bindEvents();
    this.updateDisplay();
    
    console.log('✅ Bike carousel initialized successfully');
  }
  
  bindEvents() {
    // Previous/Next buttons
    if (this.elements.prevBtn) {
      this.elements.prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.prevBike();
      });
    }
    
    if (this.elements.nextBtn) {
      this.elements.nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.nextBike();
      });
    }
    
    // Brand tabs
    this.elements.brandTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        const brand = tab.dataset.brand;
        this.switchBrand(brand);
      });
    });
    
    // Variant buttons
    this.elements.variantBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const model = btn.dataset.model;
        this.switchToModel(model);
      });
    });
  }
  
  getColorStyle(colorName) {
    const colorMap = {
      'black': '#000000',
      'white': '#ffffff',
      'red': '#dc2626',
      'blue': '#2563eb',
      'yellow': '#facc15',
      'green': '#16a34a',
      'orange': '#ea580c',
      'silver': '#6b7280',
      'maroon': '#7f1d1d'
    };
    return colorMap[colorName] || '#000000';
  }
  
  updateDisplay() {
    const brandData = this.bikeData[this.currentBrand];
    if (!brandData) {
      console.error('❌ Brand data not found for:', this.currentBrand);
      return;
    }
    
    const currentBike = brandData.bikes[this.currentBikeIndex];
    if (!currentBike) {
      console.error('❌ Bike data not found at index:', this.currentBikeIndex);
      return;
    }
    
    console.log('🔄 Updating display:', {
      brand: this.currentBrand,
      bikeIndex: this.currentBikeIndex,
      colorIndex: this.currentColorIndex,
      bike: currentBike.name
    });
    
    // Update all display elements
    this.updateBackgroundText();
    this.updateBikeInfo(currentBike);
    this.updateBrandInfo(brandData);
    this.updateColorSelection(currentBike);
    this.updateBrandTabs();
    this.updateVariantButtons();
  }
  
  updateBackgroundText() {
    if (this.elements.backgroundText) {
      this.elements.backgroundText.textContent = this.currentBrand;
    }
  }
  
  updateBikeInfo(bike) {
    if (this.elements.bikeTitle) {
      this.elements.bikeTitle.textContent = bike.name;
    }
    
    if (this.elements.bikeDescription) {
      this.elements.bikeDescription.textContent = bike.description;
    }
    
    if (this.elements.mainBikeImage) {
      this.elements.mainBikeImage.src = bike.image;
      this.elements.mainBikeImage.alt = bike.name;
    }
  }
  
  updateBrandInfo(brandData) {
    if (this.elements.brandLogo) {
      this.elements.brandLogo.src = brandData.logo;
      this.elements.brandLogo.alt = this.currentBrand + ' Logo';
    }
    
    if (this.elements.categoryIcon) {
      this.elements.categoryIcon.src = brandData.categoryIcon;
      this.elements.categoryIcon.alt = brandData.category + ' Category';
    }
    
    if (this.elements.categoryText) {
      this.elements.categoryText.textContent = brandData.category;
    }
  }
  
  updateColorSelection(bike) {
    if (!this.elements.colorSelection || !bike.colors) return;
    
    this.elements.colorSelection.innerHTML = '';
    
    bike.colors.forEach((color, index) => {
      const colorBtn = document.createElement('button');
      colorBtn.className = `color-btn w-8 h-8 rounded-full border-2 border-gray-300 ${
        index === this.currentColorIndex ? 'active' : ''
      }`;
      colorBtn.style.backgroundColor = this.getColorStyle(color.name);
      colorBtn.dataset.color = color.name;
      colorBtn.addEventListener('click', () => {
        this.currentColorIndex = index;
        this.updateDisplay();
      });
      this.elements.colorSelection.appendChild(colorBtn);
    });
  }
  
  updateBrandTabs() {
    this.elements.brandTabs.forEach(tab => {
      if (tab.dataset.brand === this.currentBrand) {
        tab.classList.add('active');
        tab.classList.remove('text-gray-400');
        tab.classList.add('text-gray-800', 'border-black');
        tab.classList.remove('border-transparent');
      } else {
        tab.classList.remove('active');
        tab.classList.add('text-gray-400');
        tab.classList.remove('text-gray-800', 'border-black');
        tab.classList.add('border-transparent');
      }
    });
  }
  
  updateVariantButtons() {
    this.elements.modelGroups.forEach(group => {
      if (group.classList.contains(this.currentBrand.toLowerCase() + '-models')) {
        group.classList.remove('hidden');
        const buttons = group.querySelectorAll('.variant-btn');
        buttons.forEach((btn, index) => {
          if (index === this.currentBikeIndex) {
            btn.classList.add('active');
            btn.classList.remove('text-gray-500', 'border-transparent');
            btn.classList.add('text-gray-700', 'border-black');
          } else {
            btn.classList.remove('active');
            btn.classList.add('text-gray-500', 'border-transparent');
            btn.classList.remove('text-gray-700', 'border-black');
          }
        });
      } else {
        group.classList.add('hidden');
      }
    });
  }
  
  nextBike() {
    const brandData = this.bikeData[this.currentBrand];
    this.currentBikeIndex = (this.currentBikeIndex + 1) % brandData.bikes.length;
    this.currentColorIndex = 0;
    this.updateDisplay();
  }
  
  prevBike() {
    const brandData = this.bikeData[this.currentBrand];
    this.currentBikeIndex = (this.currentBikeIndex - 1 + brandData.bikes.length) % brandData.bikes.length;
    this.currentColorIndex = 0;
    this.updateDisplay();
  }
  
  switchBrand(brand) {
    if (this.bikeData[brand]) {
      this.currentBrand = brand;
      this.currentBikeIndex = 0;
      this.currentColorIndex = 0;
      this.updateDisplay();
    }
  }
  
  switchToModel(modelId) {
    for (const [brand, data] of Object.entries(this.bikeData)) {
      const bikeIndex = data.bikes.findIndex(bike => bike.id === modelId);
      if (bikeIndex !== -1) {
        this.currentBrand = brand;
        this.currentBikeIndex = bikeIndex;
        this.currentColorIndex = 0;
        this.updateDisplay();
        break;
      }
    }
  }
}

// Initialize when DOM is ready and data is available
document.addEventListener('DOMContentLoaded', function() {
  // Check if bike data is available (will be provided by Laravel)
  if (typeof window.bikeData !== 'undefined') {
    window.bikeCarousel = new BikeCarousel(window.bikeData);
  } else {
    console.error('❌ Bike data not found. Make sure to define window.bikeData before including this script.');
  }
});
